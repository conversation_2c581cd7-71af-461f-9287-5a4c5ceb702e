<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>addspeedpoint</class>
 <widget class="QDialog" name="addspeedpoint">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>706</width>
    <height>581</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_6">
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="6">
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_14">
        <property name="text">
         <string>PointName:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1" colspan="3">
       <widget class="QLineEdit" name="lineEdit_name"/>
      </item>
      <item row="1" column="6">
       <widget class="QPushButton" name="pushButton_search">
        <property name="text">
         <string>search</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_11">
        <property name="text">
         <string>Area:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="comboBox_speedArea">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="label_12">
        <property name="text">
         <string>Station:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QComboBox" name="comboBox_speedStation">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="4">
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QGroupBox" name="groupBox_15">
     <property name="title">
      <string>PointList</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QListWidget" name="listWidget_speedPoint">
        <property name="selectionMode">
         <enum>QAbstractItemView::SingleSelection</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QGroupBox" name="groupBox">
        <property name="title">
         <string/>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <item row="0" column="0">
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>159</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="1" column="0">
          <widget class="QPushButton" name="pushButton_2">
           <property name="text">
            <string>&gt;&gt;</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QPushButton" name="pushButton_3">
           <property name="text">
            <string>&lt;&lt;</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
        <zorder>pushButton_2</zorder>
        <zorder>pushButton_3</zorder>
        <zorder>verticalSpacer</zorder>
        <zorder>verticalSpacer_2</zorder>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QListWidget" name="listWidget_speedPoint_2">
        <property name="selectionMode">
         <enum>QAbstractItemView::SingleSelection</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QGroupBox" name="groupBox_4">
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_5">
      <item row="0" column="0">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="1">
       <widget class="QPushButton" name="pushButton_ok">
        <property name="text">
         <string>OK</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
