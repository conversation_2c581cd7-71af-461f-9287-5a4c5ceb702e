<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TrendCurveDlg</class>
 <widget class="QDialog" name="TrendCurveDlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>793</width>
    <height>461</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>TrendCurveDlg</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="0" column="0">
    <widget class="QLabel" name="label_Info">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>1</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QLabel" name="label_Value">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="0" column="2" rowspan="2">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>Y Set</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>MinValue:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1" colspan="2">
       <widget class="QSpinBox" name="spinBox_YMinValue">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimum">
         <number>-9999</number>
        </property>
        <property name="maximum">
         <number>9999</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>MaxValue:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1" colspan="2">
       <widget class="QSpinBox" name="spinBox_YMaxValue">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimum">
         <number>-9999</number>
        </property>
        <property name="maximum">
         <number>9999</number>
        </property>
       </widget>
      </item>
      <item row="2" column="0" colspan="2">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>57</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="2" column="2">
       <widget class="QPushButton" name="pushButton_SetY">
        <property name="text">
         <string>Set</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0" rowspan="4" colspan="2">
    <widget class="UTCurve" name="curve" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
    </widget>
   </item>
   <item row="2" column="2">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>Limits Set</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>MinValue:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1" colspan="2">
       <widget class="QSpinBox" name="spinBox_LimitMinValue">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimum">
         <number>-9999</number>
        </property>
        <property name="maximum">
         <number>9999</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>MaxValue:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1" colspan="2">
       <widget class="QSpinBox" name="spinBox_LimitMaxValue">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimum">
         <number>-9999</number>
        </property>
        <property name="maximum">
         <number>9999</number>
        </property>
       </widget>
      </item>
      <item row="2" column="0" colspan="2">
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>57</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="2" column="2">
       <widget class="QPushButton" name="pushButton_SetLimit">
        <property name="text">
         <string>Set</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="2">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>175</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="4" column="2">
    <widget class="QPushButton" name="pushButton_Ok">
     <property name="text">
      <string>Ok</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>UTCurve</class>
   <extends>QWidget</extends>
   <header>utcurve.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
