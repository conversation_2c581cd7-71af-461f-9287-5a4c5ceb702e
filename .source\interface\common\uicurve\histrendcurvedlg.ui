<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HisTrendCurveDlg</class>
 <widget class="QDialog" name="HisTrendCurveDlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1191</width>
    <height>685</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>HisTrendCurveDlg</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_5">
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox_3">
     <property name="minimumSize">
      <size>
       <width>400</width>
       <height>0</height>
      </size>
     </property>
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>Station:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>PointDesc:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QLineEdit" name="lineEdit_Point">
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLineEdit" name="lineEdit_Station">
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="0">
       <widget class="QLabel" name="label_3">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>TimeType</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>From:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QWidget" name="widget_StartTime" native="true"/>
      </item>
      <item row="1" column="0">
       <widget class="QComboBox" name="comboBox_TimeType"/>
      </item>
      <item row="1" column="1">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>To:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QWidget" name="widget_EndTime" native="true"/>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="2">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="pushButton_Query">
       <property name="text">
        <string>Query</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_PrevHour">
       <property name="text">
        <string>PrevHour</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_NextHour">
       <property name="text">
        <string>NextHour</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBox_NormalScale">
       <property name="text">
        <string>NormalScale</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>359</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_Close">
       <property name="text">
        <string>Close</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0" colspan="3">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab_Curve">
      <attribute name="title">
       <string>Curve</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QScrollArea" name="scrollArea">
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>1147</width>
            <height>550</height>
           </rect>
          </property>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_Table">
      <attribute name="title">
       <string>Table</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <widget class="QTableView" name="tableView"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
