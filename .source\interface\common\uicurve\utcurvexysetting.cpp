#include "utcurvexysetting.h"

UTCurveXYSetting::UTCurveXYSetting(QObject *parent):
    QObject(parent)
{
    m_border=new UTCurveXYBorder(this);
    m_grid=new UTCurveXYGrid(this);
    m_scale=new UTCurveXYScale(this);
}

UTCurveXYBorder &UTCurveXYSetting::border(void)
{
    return(*m_border);
}

UTCurveXYGrid &UTCurveXYSetting::grid(void)
{
    return(*m_grid);
}

UTCurveXYScale &UTCurveXYSetting::scale(void)
{
    return(*m_scale);
}
