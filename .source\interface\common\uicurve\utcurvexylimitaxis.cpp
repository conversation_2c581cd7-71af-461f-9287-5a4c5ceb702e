#include "utcurvexylimitaxis.h"

UTCurveXYLimitAxis::UTCurveXYLimitAxis()
{
}

UTCurveXYLimitAxis::UTCurveXYLimitAxis(const UTCurveXYLimitAxis &limit_axis)
{
    m_minimun=limit_axis.m_minimun;
    m_maximun=limit_axis.m_maximun;
}

UTCurveXYLimitAxis::UTCurveXYLimitAxis(const QVariant &minimun,const QVariant &maximun)
{
    m_minimun=minimun;
    m_maximun=maximun;
}


QVariant UTCurveXYLimitAxis::minimun(void) const
{
    return(m_minimun);
}

QVariant UTCurveXYLimitAxis::maximun(void) const
{
    return(m_maximun);
}

void UTCurveXYLimitAxis::setLimit(const QVariant &minimun,const QVariant &maximun)
{
    m_minimun=minimun;
    m_maximun=maximun;
}


QVariant::Type UTCurveXYLimitAxis::type(void) const
{
    if(m_minimun.type()!=m_maximun.type()) return(QVariant::Invalid);

    return(m_minimun.type());
}

bool UTCurveXYLimitAxis::canConvert(QVariant::Type type) const
{
    if(
            (m_minimun.canConvert(type))&&
            (m_maximun.canConvert(type))
            )
    {
        return(true);
    }
    return(false);
}


UTCurveXYLimitAxis &UTCurveXYLimitAxis::operator=(const UTCurveXYLimitAxis &limit_axis)
{
    if(this!=&limit_axis)
    {
        m_minimun=limit_axis.m_minimun;
        m_maximun=limit_axis.m_maximun;
    }
    return(*this);
}


bool UTCurveXYLimitAxis::operator==(const UTCurveXYLimitAxis &limit_axis) const
{
    if(m_minimun!=limit_axis.m_minimun) return(false);
    if(m_maximun!=limit_axis.m_maximun) return(false);
    return(true);
}

bool UTCurveXYLimitAxis::operator!=(const UTCurveXYLimitAxis &limit_axis) const
{
    if(m_minimun!=limit_axis.m_minimun) return(true);
    if(m_maximun!=limit_axis.m_maximun) return(true);
    return(false);
}
