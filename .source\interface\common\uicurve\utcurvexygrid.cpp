#include "utcurvexygrid.h"

UTCurveXYGrid::UTCurveXYGrid(QObject *parent):
    QObject(parent)
{
    m_visible=true;
    m_border=0;
    //m_background_color=QColor(32,128,32);
    m_background_color=QColor(192,192,192);
    m_foreground_color=QColor(0,0,0);
    m_vertical_tick=new UTCurveXYGridTick();
    m_horizzontal_tick=new UTCurveXYGridTick();
}

bool UTCurveXYGrid::visible(void) const
{
    return(m_visible);
}

void UTCurveXYGrid::setVisible(bool visible)
{
    m_visible=visible;
}

unsigned int UTCurveXYGrid::border(void) const
{
    return(m_border);
}

void UTCurveXYGrid::setBorder(unsigned int border)
{
    m_border=border;
}

QColor UTCurveXYGrid::backgroundColor(void) const
{
    return(m_background_color);
}

void UTCurveXYGrid::setBackgroundColor(const QColor &color)
{
    m_background_color=color;
}

QColor UTCurveXYGrid::foregroundColor(void) const
{
    return(m_foreground_color);
}

void UTCurveXYGrid::setForegroundColor(const QColor &color)
{
    m_foreground_color=color;
}


UTCurveXYGridTick &UTCurveXYGrid::verticalTick(void)
{
    return(*m_vertical_tick);
}

UTCurveXYGridTick &UTCurveXYGrid::horizzontalTick(void)
{
    return(*m_horizzontal_tick);
}
