<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HistoryDataDlg</class>
 <widget class="QMainWindow" name="HistoryDataDlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1128</width>
    <height>802</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>HistoryDataDlg</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_8">
    <item row="0" column="0">
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tab">
       <attribute name="title">
        <string>YC</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_7">
        <item row="0" column="1" rowspan="5">
         <widget class="QTabWidget" name="tabWidget_2">
          <property name="currentIndex">
           <number>0</number>
          </property>
          <widget class="QWidget" name="tab_3">
           <attribute name="title">
            <string>Curve</string>
           </attribute>
           <layout class="QGridLayout" name="gridLayout_5">
            <item row="0" column="0">
             <widget class="QCheckBox" name="checkBox_ThirdCXCurve">
              <property name="text">
               <string>ThirdCXCurve</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="UTCurve" name="curve" native="true">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="tab_4">
           <attribute name="title">
            <string>Table</string>
           </attribute>
           <layout class="QGridLayout" name="gridLayout_6">
            <item row="0" column="0">
             <widget class="QTableView" name="tableView"/>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QGroupBox" name="groupBox_2">
          <property name="title">
           <string>QuerySet</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_2">
           <item row="0" column="0" colspan="2">
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>TimeType:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QComboBox" name="comboBox_TimeType">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="1" column="0" colspan="2">
            <widget class="QLabel" name="label_6">
             <property name="text">
              <string>DataType:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QComboBox" name="comboBox_DataType"/>
           </item>
           <item row="2" column="0" colspan="2">
            <widget class="QLabel" name="label_Normal">
             <property name="text">
              <string>Time:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="2">
            <widget class="QDateEdit" name="dateEdit_Normal"/>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_TimeTime">
             <property name="text">
              <string>Time:</string>
             </property>
            </widget>
           </item>
           <item row="3" column="1" colspan="2">
            <widget class="QDateTimeEdit" name="dateTimeEdit_Begin"/>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="label_To">
             <property name="text">
              <string>To</string>
             </property>
            </widget>
           </item>
           <item row="4" column="1" colspan="2">
            <widget class="QDateTimeEdit" name="dateTimeEdit_End"/>
           </item>
          </layout>
         </widget>
        </item>
        <item row="4" column="0" rowspan="2">
         <widget class="QGroupBox" name="groupBox_3">
          <property name="title">
           <string>CurveList</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_3">
           <item row="0" column="0" colspan="3">
            <widget class="QListWidget" name="listWidget_Curve">
             <property name="selectionMode">
              <enum>QAbstractItemView::MultiSelection</enum>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QPushButton" name="pushButton_Add">
             <property name="text">
              <string>Add</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QPushButton" name="pushButton_Delete">
             <property name="text">
              <string>Delete</string>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QPushButton" name="pushButton_Load">
             <property name="text">
              <string>Load</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="5" column="1">
         <widget class="QGroupBox" name="groupBox_4">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QGridLayout" name="gridLayout_4">
           <item row="0" column="0">
            <widget class="QPushButton" name="pushButton_PreDay">
             <property name="text">
              <string>PreDay</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QPushButton" name="pushButton_NextDay">
             <property name="text">
              <string>NextDay</string>
             </property>
            </widget>
           </item>
           <item row="0" column="9">
            <widget class="QPushButton" name="pushButton_YSet">
             <property name="text">
              <string>YSet</string>
             </property>
            </widget>
           </item>
           <item row="0" column="10" colspan="2">
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>256</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="1" column="0">
            <widget class="QPushButton" name="pushButton_RealDayStatis">
             <property name="text">
              <string>RealDayStatis</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QPushButton" name="pushButton_RealMonStatis">
             <property name="text">
              <string>RealMonStatis</string>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QPushButton" name="pushButton_RealYearStatis">
             <property name="text">
              <string>RealYearStatis</string>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QPushButton" name="pushButton_HisDayStatis">
             <property name="text">
              <string>HisDayStatis</string>
             </property>
            </widget>
           </item>
           <item row="1" column="4">
            <widget class="QPushButton" name="pushButton_HisMonStatis">
             <property name="text">
              <string>HisMonStatis</string>
             </property>
            </widget>
           </item>
           <item row="1" column="5">
            <widget class="QPushButton" name="pushButton_HisYearStatis">
             <property name="text">
              <string>HisYearStatis</string>
             </property>
            </widget>
           </item>
           <item row="1" column="6">
            <widget class="QPushButton" name="pushButton_RealTrend">
             <property name="text">
              <string>RealTrend</string>
             </property>
            </widget>
           </item>
           <item row="1" column="9" colspan="2">
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>256</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="1" column="11">
            <widget class="QPushButton" name="pushButton_Close">
             <property name="text">
              <string>Close</string>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QPushButton" name="pushButton_StatisData">
             <property name="text">
              <string>StatisData</string>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QPushButton" name="pushButton_Print">
             <property name="text">
              <string>Print</string>
             </property>
            </widget>
           </item>
           <item row="0" column="4">
            <widget class="QPushButton" name="pushButton_ExportTable">
             <property name="text">
              <string>ExportTable</string>
             </property>
            </widget>
           </item>
           <item row="0" column="5">
            <widget class="QPushButton" name="pushButton_EditData">
             <property name="text">
              <string>EditData</string>
             </property>
            </widget>
           </item>
           <item row="0" column="6">
            <widget class="QPushButton" name="pushButton_CopyData">
             <property name="text">
              <string>CopyData</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="0" rowspan="3">
         <widget class="QGroupBox" name="groupBox_17">
          <property name="title">
           <string>PointList</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_25">
           <item row="1" column="0">
            <widget class="QGroupBox" name="groupBox">
             <property name="title">
              <string/>
             </property>
             <layout class="QGridLayout" name="gridLayout">
              <item row="3" column="1" colspan="2">
               <widget class="QComboBox" name="comboBox_YCArea">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
               </widget>
              </item>
              <item row="4" column="1" colspan="2">
               <widget class="QComboBox" name="comboBox_YCStation">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="editable">
                 <bool>false</bool>
                </property>
               </widget>
              </item>
              <item row="5" column="1" colspan="2">
               <widget class="QComboBox" name="comboBox_YCMeasType">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QLabel" name="label_2">
                <property name="text">
                 <string>Station:</string>
                </property>
               </widget>
              </item>
              <item row="5" column="0">
               <widget class="QLabel" name="label_3">
                <property name="text">
                 <string>MeasType:</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="label">
                <property name="text">
                 <string>Area:</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QListWidget" name="listWidget_YCPoint">
             <property name="selectionMode">
              <enum>QAbstractItemView::SingleSelection</enum>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QGroupBox" name="groupBox_16">
             <property name="title">
              <string/>
             </property>
             <layout class="QGridLayout" name="gridLayout_24">
              <item row="0" column="0">
               <widget class="QLabel" name="label_14">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>PointName:</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <spacer name="horizontalSpacer_6">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>60</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item row="0" column="2">
               <widget class="QPushButton" name="pushButton_find">
                <property name="text">
                 <string>findPoint</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0" colspan="3">
               <widget class="QLineEdit" name="lineEdit_point"/>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_2">
       <attribute name="title">
        <string>YX</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_10">
        <item row="0" column="0" rowspan="2">
         <widget class="QGroupBox" name="groupBox_5">
          <property name="maximumSize">
           <size>
            <width>250</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="title">
           <string>PointList</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_9">
           <item row="0" column="0" colspan="2">
            <widget class="QGroupBox" name="groupBox_18">
             <property name="title">
              <string/>
             </property>
             <layout class="QGridLayout" name="gridLayout_26">
              <item row="0" column="0">
               <widget class="QLabel" name="label_21">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>PointName:</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <spacer name="horizontalSpacer_11">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>60</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item row="0" column="2">
               <widget class="QPushButton" name="pushButton_find_YX">
                <property name="text">
                 <string>findPoint</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0" colspan="3">
               <widget class="QLineEdit" name="lineEdit_point_yx"/>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>Area:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QComboBox" name="comboBox_YXArea">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_7">
             <property name="text">
              <string>Station:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QComboBox" name="comboBox_YXStation">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="editable">
              <bool>false</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_8">
             <property name="text">
              <string>MeasType:</string>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QComboBox" name="comboBox_YXMeasType">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="label_Normal_2">
             <property name="text">
              <string>Time:</string>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QDateEdit" name="dateEdit_YXNormal"/>
           </item>
           <item row="5" column="0" colspan="2">
            <widget class="QListWidget" name="listWidget_YXPoint">
             <property name="selectionMode">
              <enum>QAbstractItemView::SingleSelection</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QTableView" name="tableView_YX"/>
        </item>
        <item row="1" column="1">
         <widget class="QGroupBox" name="groupBox_6">
          <property name="title">
           <string/>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QPushButton" name="pushButton_YXHisDayStatis">
             <property name="text">
              <string>HisDayStatis</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_YXHisMonStatis">
             <property name="text">
              <string>HisMonStatis</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_YXHisYearStatis">
             <property name="text">
              <string>HisYearStatis</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_YXStatisData">
             <property name="text">
              <string>StatisData</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>464</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_YXClose">
             <property name="text">
              <string>Close</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_5">
       <attribute name="title">
        <string>Rtu</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_12">
        <item row="0" column="0" rowspan="2">
         <widget class="QGroupBox" name="groupBox_7">
          <property name="maximumSize">
           <size>
            <width>250</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="title">
           <string>RtuList</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_11">
           <item row="0" column="0">
            <widget class="QLabel" name="label_9">
             <property name="text">
              <string>Area:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QComboBox" name="comboBox_RtuArea">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_10">
             <property name="text">
              <string>Station:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QComboBox" name="comboBox_RtuStation">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_Normal_3">
             <property name="text">
              <string>Time:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QDateEdit" name="dateEdit_RtuNormal"/>
           </item>
           <item row="3" column="0" colspan="2">
            <widget class="QListWidget" name="listWidget_Rtu">
             <property name="selectionMode">
              <enum>QAbstractItemView::SingleSelection</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QTableView" name="tableView_Rtu"/>
        </item>
        <item row="1" column="1">
         <widget class="QGroupBox" name="groupBox_8">
          <property name="title">
           <string/>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <widget class="QPushButton" name="pushButton_RtuHisMonStatis">
             <property name="text">
              <string>HisMonStatis</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_RtuHisYearStatis">
             <property name="text">
              <string>HisYearStatis</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_RtuStatisData">
             <property name="text">
              <string>StatisData</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>464</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_RtuClose">
             <property name="text">
              <string>Close</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_6">
       <attribute name="title">
        <string>Channel</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_15">
        <item row="0" column="0" rowspan="2">
         <widget class="QGroupBox" name="groupBox_10">
          <property name="maximumSize">
           <size>
            <width>250</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="title">
           <string>ChannelList</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_14">
           <item row="0" column="0">
            <widget class="QLabel" name="label_15">
             <property name="text">
              <string>Area:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QComboBox" name="comboBox_ChannelArea">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_16">
             <property name="text">
              <string>Station:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QComboBox" name="comboBox_ChannelStation">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_Normal_6">
             <property name="text">
              <string>Time:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QDateEdit" name="dateEdit_ChannelNormal"/>
           </item>
           <item row="3" column="0" colspan="2">
            <widget class="QListWidget" name="listWidget_Channel">
             <property name="selectionMode">
              <enum>QAbstractItemView::SingleSelection</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QTableView" name="tableView_Channel"/>
        </item>
        <item row="1" column="1">
         <widget class="QGroupBox" name="groupBox_9">
          <property name="title">
           <string/>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QPushButton" name="pushButton_ChannelHisMonStatis">
             <property name="text">
              <string>HisMonStatis</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_ChannelHisYearStatis">
             <property name="text">
              <string>HisYearStatis</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_ChannelStatisData">
             <property name="text">
              <string>StatisData</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_7">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>464</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_ChannelClose">
             <property name="text">
              <string>Close</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_7">
       <attribute name="title">
        <string>ReCall</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_21">
        <item row="0" column="0" rowspan="3">
         <widget class="QGroupBox" name="groupBox_11">
          <property name="maximumSize">
           <size>
            <width>250</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="title">
           <string>PointList</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_17">
           <item row="0" column="0">
            <widget class="QLabel" name="label_17">
             <property name="text">
              <string>Area:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QComboBox" name="comboBox_ReCallArea">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_18">
             <property name="text">
              <string>Station:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QComboBox" name="comboBox_ReCallStation">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_20">
             <property name="text">
              <string>YXYCType:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QComboBox" name="comboBox_ReCallYXYC"/>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_19">
             <property name="text">
              <string>MeasType:</string>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QComboBox" name="comboBox_ReCallMeasType">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="4" column="0" colspan="2">
            <widget class="QListWidget" name="listWidget_ReCallPoint">
             <property name="selectionMode">
              <enum>QAbstractItemView::SingleSelection</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QGroupBox" name="groupBox_12">
          <property name="title">
           <string>TimeSet</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_13">
           <item row="0" column="0">
            <widget class="QLabel" name="label_TimeTime_2">
             <property name="text">
              <string>Time:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QDateTimeEdit" name="dateTimeEdit_ReCallBegin"/>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="label_To_2">
             <property name="text">
              <string>To</string>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QDateTimeEdit" name="dateTimeEdit_ReCallEnd"/>
           </item>
           <item row="0" column="4">
            <widget class="QPushButton" name="pushButton_ReCallLoad">
             <property name="text">
              <string>Load</string>
             </property>
            </widget>
           </item>
           <item row="0" column="5">
            <spacer name="horizontalSpacer_9">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>457</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="1" column="0" colspan="6">
            <widget class="QTableView" name="tableView_ReCallSource"/>
           </item>
           <item row="2" column="0" colspan="6">
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <spacer name="horizontalSpacer_8">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>638</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_ReCallDelete">
               <property name="text">
                <string>Delete</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_ReCallLoadData">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>LoadData</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QGroupBox" name="groupBox_13">
          <property name="title">
           <string>DataInfo</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_19">
           <item row="0" column="0">
            <widget class="QTableView" name="tableView_ReCallData"/>
           </item>
          </layout>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QGroupBox" name="groupBox_14">
          <property name="title">
           <string/>
          </property>
          <layout class="QGridLayout" name="gridLayout_20">
           <item row="0" column="0">
            <spacer name="horizontalSpacer_10">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>464</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="0" column="1">
            <widget class="QPushButton" name="pushButton_ReCallClose">
             <property name="text">
              <string>Close</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_8">
       <attribute name="title">
        <string>speed</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_23">
        <item row="0" column="0">
         <widget class="QGroupBox" name="groupBox_15">
          <property name="title">
           <string>PointList</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_22">
           <item row="0" column="0">
            <widget class="QLabel" name="label_11">
             <property name="text">
              <string>Area:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1" colspan="3">
            <widget class="QComboBox" name="comboBox_speedArea">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_12">
             <property name="text">
              <string>Station:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1" colspan="3">
            <widget class="QComboBox" name="comboBox_speedStation">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_13">
             <property name="text">
              <string>MeasType:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1" colspan="3">
            <widget class="QComboBox" name="comboBox_speedMeasType">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item row="3" column="0" colspan="4">
            <widget class="QListWidget" name="listWidget_speedPoint">
             <property name="selectionMode">
              <enum>QAbstractItemView::SingleSelection</enum>
             </property>
            </widget>
           </item>
           <item row="4" column="0" colspan="2">
            <widget class="QPushButton" name="pushButton_speedAdd">
             <property name="text">
              <string>Add</string>
             </property>
            </widget>
           </item>
           <item row="4" column="2">
            <widget class="QPushButton" name="pushButton_speedDelete">
             <property name="text">
              <string>Delete</string>
             </property>
            </widget>
           </item>
           <item row="4" column="3">
            <widget class="QPushButton" name="pushButton_speedLoad">
             <property name="text">
              <string>Load</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QTabWidget" name="tabWidget_speed">
          <property name="currentIndex">
           <number>0</number>
          </property>
          <widget class="QWidget" name="tab_9">
           <attribute name="title">
            <string>Curve</string>
           </attribute>
           <layout class="QGridLayout" name="gridLayout_16">
            <item row="1" column="1">
             <widget class="QPushButton" name="pushButton_speedClose">
              <property name="text">
               <string>Close</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <spacer name="horizontalSpacer_5">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="0" column="0" colspan="2">
             <widget class="UTCurve" name="curve_speed" native="true">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="tab_10">
           <attribute name="title">
            <string>Table</string>
           </attribute>
           <layout class="QGridLayout" name="gridLayout_18">
            <item row="0" column="0">
             <widget class="QTableView" name="tableView_speed"/>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_11">
       <attribute name="title">
        <string>ManualMonthData</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_5">
          <item>
           <widget class="QLabel" name="label_25">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Station:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBox_manualStation">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_22">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>PointDesc:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBox_manualPointDesc">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_23">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Time:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDateEdit" name="dateEdit_manualTime">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_24">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>DataType:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBox_manualDataType">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_manualQry">
            <property name="text">
             <string>Query</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_13">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Preferred</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <item>
           <widget class="QPushButton" name="pushButton_manulAdd">
            <property name="text">
             <string>Add</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_manulEdit">
            <property name="text">
             <string>Edit</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_manulSave">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>Save</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_manulCancel">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>Cancel</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_manulDel">
            <property name="text">
             <string>Delete</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_import">
            <property name="text">
             <string>Import</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_12">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>318</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTableView" name="tableView_manualData"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_12">
       <attribute name="title">
        <string>RepairHisInfo</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_8">
        <item row="0" column="0">
         <widget class="QLabel" name="label_31">
          <property name="text">
           <string>station</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="lineEdit_JXStationQuery">
          <property name="maximumSize">
           <size>
            <width>150</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="0" column="2" rowspan="2">
         <widget class="QGroupBox" name="groupBox_19">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_9">
           <item>
            <widget class="QLabel" name="label_27">
             <property name="text">
              <string>PointDesc</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_JXPointDesc">
             <property name="maximumSize">
              <size>
               <width>160</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="checkBox_lastJXTime">
             <property name="text">
              <string>lastJXTime</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QDateTimeEdit" name="dateTimeEdit_startLastJXTime">
             <property name="displayFormat">
              <string>yyyy-MM-dd HH:mm:ss</string>
             </property>
             <property name="calendarPopup">
              <bool>false</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_29">
             <property name="text">
              <string>To</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QDateTimeEdit" name="dateTimeEdit_endLastJXTime">
             <property name="displayFormat">
              <string>yyyy-MM-dd HH:mm:ss</string>
             </property>
             <property name="calendarPopup">
              <bool>false</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_jxquery">
             <property name="maximumSize">
              <size>
               <width>75</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Query</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_15">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item row="1" column="0" rowspan="2" colspan="2">
         <widget class="QListWidget" name="listWidget_JXStation">
          <property name="maximumSize">
           <size>
            <width>250</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QTableView" name="tableView_JXtable"/>
        </item>
        <item row="3" column="0" colspan="3">
         <layout class="QHBoxLayout" name="horizontalLayout_7">
          <item>
           <widget class="QPushButton" name="pushButton_allSelect">
            <property name="text">
             <string>selectAll</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_noSelect">
            <property name="text">
             <string>NoSelect</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_14">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>498</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>UTCurve</class>
   <extends>QWidget</extends>
   <header>utcurve.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
