#ifndef HISDATACOPYDLG_H
#define HISDATACOPYDLG_H

#include <QDialog>
#include "uiquery.h"

extern "C" {
#include "gdadef.h"
}
namespace Ui {
class HisDataCopyDlg;
}

class QTimer;
class HisDataCopyDlg : public QDialog
{
    Q_OBJECT
    
public:
    explicit HisDataCopyDlg(QWidget *parent = 0);
    ~HisDataCopyDlg();
    
    void showDlg();
    QTimer * m_timer;

    time_t m_starttime;
    time_t m_endtime;
    time_t m_tostarttime;
    time_t m_toendtime;

    int	m_copyFlag;
    int m_addCount;
    QList<int> m_pointList;

    CDA_SES_HANDLE m_cdaSessionHandle;

    void setPointPara(QList<int> &list);

    void setSessionHandle(CDA_SES_HANDLE handle);
public slots:
    void BatchCopyOnePointData();
    void DataBatchCopySlots();
    void StopBatchCopySlots();
    void closeSlots();
private:
    Ui::HisDataCopyDlg *ui;
};

#endif // HISDATACOPYDLG_H
