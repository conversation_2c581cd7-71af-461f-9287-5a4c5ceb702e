#ifndef UTCURVEXYFUNCTION_H
#define UTCURVEXYFUNCTION_H
#include <QVariant>
#include <QDateTime>

class UTCurveXYFunction
{
public:
    UTCurveXYFunction();
public:
  // This function convert variant to absolute qreal
  static qreal variantToAbsolute(const QVariant &variant);

  static QVariant uint64ToVariantDateTime(const quint64 value);
  static QVariant uint64ToVariantDate(const quint64 value);
  static QVariant uint64ToVariantTime(const quint64 value);
};

#endif // UTCURVEXYFUNCTION_H
