<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UtActSpeedDlg</class>
 <widget class="QDialog" name="UtActSpeedDlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>820</width>
    <height>550</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>820</width>
    <height>550</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>820</width>
    <height>550</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="2" column="3">
    <widget class="QPushButton" name="pushButton_add">
     <property name="text">
      <string>addpoint</string>
     </property>
    </widget>
   </item>
   <item row="2" column="4">
    <widget class="QPushButton" name="pushButton_Close">
     <property name="text">
      <string>Close</string>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="5">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>curvetab</string>
      </attribute>
      <widget class="UTCurve" name="curve" native="true">
       <property name="geometry">
        <rect>
         <x>9</x>
         <y>9</y>
         <width>778</width>
         <height>401</height>
        </rect>
       </property>
      </widget>
      <widget class="QFrame" name="frame">
       <property name="geometry">
        <rect>
         <x>9</x>
         <y>411</y>
         <width>781</width>
         <height>41</height>
        </rect>
       </property>
       <property name="frameShape">
        <enum>QFrame::NoFrame</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="1">
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="3">
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="5">
         <spacer name="horizontalSpacer_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_1">
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="9">
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>419</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="4">
         <widget class="QLabel" name="label_3">
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="6">
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="8">
         <widget class="QLabel" name="label_5">
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="7">
         <spacer name="horizontalSpacer_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>tabletab</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="0" column="0">
        <widget class="QTableView" name="tableView"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item row="0" column="0" colspan="5">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>Active Speed:</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButton_on">
       <property name="text">
        <string>on</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButton_off">
       <property name="text">
        <string>off</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButton_all">
       <property name="text">
        <string>all</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="1">
    <widget class="QPushButton" name="pushButton_config">
     <property name="text">
      <string>config</string>
     </property>
    </widget>
   </item>
   <item row="2" column="2">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>637</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>UTCurve</class>
   <extends>QWidget</extends>
   <header>utcurve.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
