#include <QDebug>
#include <QFile>

#include "curecfg.h"
#include "ui_curecfg.h"

curecfg::curecfg(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::curecfg)
{
    ui->setupUi(this);

    iniui();
}

curecfg::~curecfg()
{
    delete ui;
}

void curecfg::iniui()
{
    QString strfile = "/utplat/proj/curveconfig.ini";
    QFile file(strfile);
    if(file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream inout(&file);
        QString strcontent = inout.readAll();
        QStringList strlist = strcontent.split(";");

        if (strlist.count() < 3)
            return ;

        m_fdata = strlist.at(0).toDouble();
        m_ndir = strlist.at(1).toInt();
        m_nMax = strlist.at(2).toInt();

        //qDebug() << "strlist.count= " << strlist.count() << "data" << m_fdata << "dir" << m_ndir << "max" << m_nMax;

        //out.setCodec(encodestr.toLocal8Bit());
        //out<<strlist.join("\r\n");

        file.close();
    }
    else
    {
        return;
    }

    ui->lineEdit->insert(QString("%1").arg(m_fdata));
    ui->lineEdit_2->insert(QString("%1").arg(m_ndir));
    ui->lineEdit_3->insert(QString("%1").arg(m_nMax));
}

void curecfg::on_buttonBox_accepted()
{
    m_fdata = ui->lineEdit->text().toDouble();
    m_ndir = ui->lineEdit_2->text().toInt();
    m_nMax = ui->lineEdit_3->text().toInt();
    QString strcon = QString("%1;%2;%3").arg(m_fdata).arg(m_ndir).arg(m_nMax);
    //qDebug() << "+++++++++++++++++" << strcon;

    QString strfile = "/utplat/proj/curveconfig.ini";
    QFile file(strfile);
    if(file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QTextStream inout(&file);
        inout << strcon;

        this->accept();
    }
    else
    {
        this->reject();
    }
}
