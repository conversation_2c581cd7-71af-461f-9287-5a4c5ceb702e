#include <QMessageBox>
#include <QDebug>
#include "historydatadlg.h"
#include "ui_historydatadlg.h"

int HistoryDataDlg::fetchStatisticData(int pid,const QDate & certaindate,
                                            int timetype,
                                            int statistictype)
{
    QDateTime datetime(certaindate,QTime(8,0,0),Qt::LocalTime);

    time_t statistictime;
    int devicetype;
    QDateTime tmpdt;
    //uint16 * dataflag = NULL;
    //int dataflagNum = 0;
    QList<int> m_idlist;
    QStandardItemModel *tmptablemodel=NULL;
    switch( statistictype )
    {
    case DEVICE_STATISTICTYPE_BREAK:
        m_yxtablemodel->setRowCount(0);
        break;
    case DEVICE_STATISTICTYPE_RTU:
        m_rtutablemodel->setRowCount(0);
        break;
    case DEVICE_STATISTICTYPE_ROUTE:
        m_channeltablemodel->setRowCount(0);
        break;
    default:
        return 0;
    }

    switch( statistictype )
    {
    case DEVICE_STATISTICTYPE_BREAK:
        m_idlist = m_yxStatisIdList;
        devicetype = HIS_DEV_TYPE_POINT;
        break;
    case DEVICE_STATISTICTYPE_RTU:
        m_idlist = m_rtuStatisIdList;
        devicetype = HIS_DEV_TYPE_RTU;
        break;
    case DEVICE_STATISTICTYPE_ROUTE:
        m_idlist = m_channelStatisIdList;
        devicetype = HIS_DEV_TYPE_ROUTE;
        break;
    default:
        return FALSE;
    }

    QString caption;
    switch( timetype )
    {
    case UTCurve_DayCurve:
        tmpdt = datetime;
        statistictime = tmpdt.toTime_t();
        caption = tmpdt.toString("yyyy.MM.dd") + tr("Day");
        break;
    case UTCurve_MonCurve:
        //set first day of a month
        tmpdt = datetime.addDays(1-datetime.date().day());
        statistictime = tmpdt.toTime_t();
        //xxw modify
        //caption = tmpdt.toString("yyyy.MM.dd") + tr("Month");
        caption = tmpdt.toString("yyyy.MM") + tr("Month");
        break;
    case UTCurve_YearCurve:
        //set first day, January of a year
        tmpdt = datetime.addDays(1-datetime.date().day());
        tmpdt = tmpdt.addMonths(1-datetime.date().month());
        statistictime = tmpdt.toTime_t();
        caption = tmpdt.toString("yyyy") + tr("Year");
        break;
    default:
        return FALSE;
    }
    caption += " " + tr("Statistic Information");
    //setWindowTitle(caption);

#ifndef HISTORYDATE_IS_LOCALTIME
    /* convert time to Greenwich time */
    sint32 tzSec = 0;
    //UTL_GetTimeZone( &tzSec );
    //statistictime -= tzSec;
#endif
    DeviceStatisticValue* pDeviceStatisticValue = NULL;

    printf(" q30>>>.the point_id= %d , statistictime = %d , timetype = %d, \n",
           pid,statistictime,timetype);

    printf(" \n >>>devicetype=%d \n",
           devicetype);

   // printf(" \n dataflag[0]= %d, dataflag[1]= %d, dataflag[2]= %d\n",
   //        dataflag[0],dataflag[1],dataflag[2] );

    int nRet = FetchDeviceHistoryStatistics(m_cdaSessionHandle,
                                            pid, statistictime, timetype,
                                            devicetype,
                                            m_idlist,
                                            pDeviceStatisticValue);

    if( nRet <= 0 )
    {
        QMessageBox::information(this,tr("Info"),tr("Have no data from database!"));
        return FALSE;
    }

    updateToListView(nRet, pDeviceStatisticValue, timetype, statistictype);

    if( pDeviceStatisticValue ) delete [] pDeviceStatisticValue;

    return TRUE;
}

void HistoryDataDlg::updateToListView(
    int valueNum, DeviceStatisticValue* & pDeviceStatisticValue,
    int timetype, int statistictype)
{
    int i=0, j=0;

    QStandardItemModel *tmptablemodel=NULL;
    switch( statistictype )
    {
    case DEVICE_STATISTICTYPE_BREAK:
        tmptablemodel=m_yxtablemodel;
        break;
    case DEVICE_STATISTICTYPE_RTU:
        tmptablemodel=m_rtutablemodel;
        break;
    case DEVICE_STATISTICTYPE_ROUTE:
        tmptablemodel=m_channeltablemodel;
        break;
    default:
        return;
    }

    tmptablemodel->removeRows(0,tmptablemodel->rowCount());
    //ui->m_statisticListView->clear();

    if( valueNum <= 0 || !pDeviceStatisticValue )
        return;

    QString labelstring;
    QDateTime dt;
    QList<QString> m_desclist;
    QList<int> m_idlist;
    switch( statistictype )
    {
    case DEVICE_STATISTICTYPE_BREAK:
        m_idlist = m_yxStatisIdList;
        m_desclist = m_yxStatisDescList;
        break;
    case DEVICE_STATISTICTYPE_RTU:
        m_idlist = m_rtuStatisIdList;
        m_desclist = m_rtuStatisDescList;
        break;
    case DEVICE_STATISTICTYPE_ROUTE:
        m_idlist = m_channelStatisIdList;
        m_desclist = m_channelStatisDescList;
        break;
    default:
        return;
    }

    int findFlag=0;
    tmptablemodel->setRowCount(m_desclist.count());
    for(i=0; i<m_desclist.count(); i++)
    {
        tmptablemodel->setData(tmptablemodel->index(i, 0, QModelIndex()),
                              m_desclist.at(i));

        findFlag=0;

        for(j=0; j<valueNum; j++)
        {
            if( pDeviceStatisticValue[j].flag == m_idlist.at(i) )
            {
                //qDebug()<<"j"<<j
                //<<"i"<<i
                //<<"valflag"<<pDeviceStatisticValue[j].flag
                //<<"val"<<pDeviceStatisticValue[j].value
                //<<"dataflag"<<dataflag[i];
                labelstring = labelstring.setNum(pDeviceStatisticValue[j].value);
                tmptablemodel->setData(tmptablemodel->index(i, 1, QModelIndex()),
                                      QString::number(pDeviceStatisticValue[j].value));

                findFlag=1;
                break;
            }
        }
        if( findFlag == 0 )
        {
            tmptablemodel->setData(tmptablemodel->index(i, 1, QModelIndex()),"----");

        }
    }
}
