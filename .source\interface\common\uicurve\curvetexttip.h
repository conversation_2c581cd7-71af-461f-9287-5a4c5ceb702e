#ifndef CURVETEXTTIP_H
#define CURVETEXTTIP_H

class QWidget;
class QLabel;
class QString;
class CurveTextTip
{
public:
    QLabel* m_pPopMsg;
    int m_returnCountFlag;
    int m_width;
    int m_height;
    int m_lastx;
    int m_lasty;
    int m_lastOriY;
    int m_bidId;

public:
    CurveTextTip();
    ~CurveTextTip();
    void displayPopTip(int x, int y, const QString &tip, QWidget* wgt);
    void getTipPos(char* tip, QWidget* wgt);
    void hidePopTip();
    bool tipIsShow();
    void moveTo(int x, int y);
    void moveXTo(int x);
    void moveYTo(int y);
    void setBidId(int id);
};

#endif // CURVETEXTTIP_H
