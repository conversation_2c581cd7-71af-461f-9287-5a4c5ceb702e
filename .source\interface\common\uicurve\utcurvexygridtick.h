#ifndef UTCURVEXYGRIDTICK_H
#define UTCURVEXYGRIDTICK_H

#include <QObject>

class UTCurveXYGridTick : public QObject
{
    Q_OBJECT
public:
    UTCurveXYGridTick();
    
    UTCurveXYGridTick(unsigned int tick_major,unsigned int tick_minor);

    unsigned int tickMajor(void) const;
    void setTickMajor(unsigned int tick);

    unsigned int tickMinor(void) const;
    void setTickMinor(unsigned int tick);
private:
    unsigned int m_major;
    unsigned int m_minor;
    
};

#endif // UTCURVEXYGRIDTICK_H
