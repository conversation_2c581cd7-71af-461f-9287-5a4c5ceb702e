<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HisDataCopyDlg</class>
 <widget class="QDialog" name="HisDataCopyDlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>396</width>
    <height>224</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>HisDataCopyDlg</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="0" column="0">
    <widget class="QLabel" name="label_Info">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="1" rowspan="2">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>64</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0" rowspan="2">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>SourceData</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>StartTime:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QDateTimeEdit" name="dateTimeEdit_SourceStartTime">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>EndTime:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDateTimeEdit" name="dateTimeEdit_SourceEndTime">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="QPushButton" name="pushButton_StartCopy">
     <property name="text">
      <string>StartCopy</string>
     </property>
    </widget>
   </item>
   <item row="3" column="0" rowspan="3">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>TargetData</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>StartTime:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QDateTimeEdit" name="dateTimeEdit_TargetStartTime">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>EndTime:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDateTimeEdit" name="dateTimeEdit_TargetEndTime">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="1">
    <widget class="QPushButton" name="pushButton_StopCopy">
     <property name="text">
      <string>StopCopy</string>
     </property>
    </widget>
   </item>
   <item row="4" column="1">
    <widget class="QPushButton" name="pushButton_Close">
     <property name="text">
      <string>Close</string>
     </property>
    </widget>
   </item>
   <item row="5" column="1">
    <spacer name="verticalSpacer_2">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>15</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="6" column="0">
    <widget class="QLabel" name="label_State">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
