<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HisDataEditDlg</class>
 <widget class="QDialog" name="HisDataEditDlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>847</width>
    <height>611</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>HisDataEditDlg</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="0" column="0">
    <widget class="QLabel" name="label_Info">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QDateEdit" name="dateEdit"/>
   </item>
   <item row="1" column="0" rowspan="6">
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QTableView" name="tableView">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>250</height>
       </size>
      </property>
     </widget>
     <widget class="UTCurve" name="curve" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>TimeType</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QRadioButton" name="radioButton_Day">
        <property name="text">
         <string>Day</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QRadioButton" name="radioButton_Month">
        <property name="text">
         <string>Month</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string>DataType</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QRadioButton" name="radioButton_HisData">
        <property name="text">
         <string>His Data</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QRadioButton" name="radioButton_RealMax">
        <property name="text">
         <string>Real Max</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QRadioButton" name="radioButton_RealMin">
        <property name="text">
         <string>Real Min</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QRadioButton" name="radioButton_RealAverage">
        <property name="text">
         <string>Real Average</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QRadioButton" name="radioButton_RealHGL">
        <property name="text">
         <string>Real HGL</string>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="QRadioButton" name="radioButton_RealPqrate">
        <property name="text">
         <string>Real Pqrate</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="1">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>219</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="4" column="1">
    <widget class="QPushButton" name="pushButton_Load">
     <property name="text">
      <string>Load</string>
     </property>
    </widget>
   </item>
   <item row="5" column="1">
    <widget class="QPushButton" name="pushButton_Edit">
     <property name="text">
      <string>Edit</string>
     </property>
    </widget>
   </item>
   <item row="6" column="1">
    <widget class="QPushButton" name="pushButton_Close">
     <property name="text">
      <string>Close</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>UTCurve</class>
   <extends>QWidget</extends>
   <header>utcurve.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
