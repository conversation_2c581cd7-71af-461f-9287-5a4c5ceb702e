#ifndef UTCURVEXYLIMIT_H
#define UTCURVEXYLIMIT_H

#include <QObject>
#include "utcurvexylimitaxis.h"
class UTCurveXYLimit
{
public:
    UTCurveXYLimit();
    
    UTCurveXYLimit(const UTCurveXYLimit &limit);
    UTCurveXYLimit(const UTCurveXYLimitAxis &limit_x,const UTCurveXYLimitAxis &limit_y);

    const UTCurveXYLimitAxis &limitX(void) const;
    const UTCurveXYLimitAxis &limitY(void) const;

    QVariant::Type type(void) const;
    bool canConvert(QVariant::Type type) const;


    UTCurveXYLimit &operator=(const UTCurveXYLimit &limit);
    bool operator==(const UTCurveXYLimit &limit) const;
    bool operator!=(const UTCurveXYLimit &limit) const;
private:
    UTCurveXYLimitAxis m_limit_x;
    UTCurveXYLimitAxis m_limit_y;
    
};

#endif // UTCURVEXYLIMIT_H
