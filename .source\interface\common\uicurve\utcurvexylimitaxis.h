#ifndef UTCURVEXYLIMITAXIS_H
#define UTCURVEXYLIMITAXIS_H

#include <QObject>
#include <QVariant>
class UTCurveXYLimitAxis
{
public:
    UTCurveXYLimitAxis();
    
    UTCurveXYLimitAxis(const UTCurveXYLimitAxis &limit_axis);
    UTCurveXYLimitAxis(const QVariant &minimun,const QVariant &maximun);

    QVariant minimun(void) const;
    QVariant maximun(void) const;
    void setLimit(const QVariant &minimun,const QVariant &maximun);

    QVariant::Type type(void) const;
    bool canConvert(QVariant::Type type) const;

    UTCurveXYLimitAxis &operator=(const UTCurveXYLimitAxis &limit_axis);
    bool operator==(const UTCurveXYLimitAxis &limit_axis) const;
    bool operator!=(const UTCurveXYLimitAxis &limit_axis) const;
signals:

public slots:
private:
    QVariant m_minimun;
    QVariant m_maximun;
    
};

#endif // UTCURVEXYLIMITAXIS_H
