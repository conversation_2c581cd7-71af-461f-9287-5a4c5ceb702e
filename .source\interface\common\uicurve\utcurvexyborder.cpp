#include "utcurvexyborder.h"

UTCurveXYBorder::UTCurveXYBorder(QObject *parent):
    QObject(parent)
{
    m_top=16;
    m_right=16;
    m_bottom=40;
    m_left=64;
    m_background_color=Qt::white;
}

unsigned int UTCurveXYBorder::top(void) const
{
    return(m_top);
}

unsigned int UTCurveXYBorder::right(void) const
{
    return(m_right);
}

unsigned int UTCurveXYBorder::bottom(void) const
{
    return(m_bottom);
}

unsigned int UTCurveXYBorder::left(void) const
{
    return(m_left);
}

void UTCurveXYBorder::setTop(unsigned int value)
{
    m_top=value;
}

void UTCurveXYBorder::setRight(unsigned int value)
{
    m_right=value;
}

void UTCurveXYBorder::setBottom(unsigned int value)
{
    m_bottom=value;
}

void UTCurveXYBorder::setLeft(unsigned int value)
{
    m_left=value;
}

QColor UTCurveXYBorder::backgroundColor(void) const
{
    return(m_background_color);
}

void UTCurveXYBorder::setBackgroundColor(const QColor &color)
{
    m_background_color=color;
}
