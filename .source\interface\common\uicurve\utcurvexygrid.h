#ifndef UTCURVEXYGRID_H
#define UTCURVEXYGRID_H

#include <QObject>
#include <QColor>
#include "utcurvexygridtick.h"
class UTCurveXYGrid : public QObject
{
    Q_OBJECT
public:
    explicit UTCurveXYGrid(QObject *parent = 0);
    
    bool visible(void) const;
    void setVisible(bool visible);

    unsigned int border(void) const;
    void setBorder(unsigned int border);

    QColor backgroundColor(void) const;
    void setBackgroundColor(const QColor &color);

    QColor foregroundColor(void) const;
    void setForegroundColor(const QColor &color);

    UTCurveXYGridTick &verticalTick(void);
    UTCurveXYGridTick &horizzontalTick(void);
signals:

public slots:

private:
    bool m_visible;
    unsigned int m_border; // C
    QColor m_background_color;
    QColor m_foreground_color;
    UTCurveXYGridTick *m_vertical_tick;
    UTCurveXYGridTick *m_horizzontal_tick;
    
};

#endif // UTCURVEXYGRID_H
