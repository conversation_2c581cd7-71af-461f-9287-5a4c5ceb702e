#ifndef UTCURVEXYSETTING_H
#define UTCURVEXYSETTING_H

#include <QObject>
#include "utcurvexyborder.h"
#include "utcurvexygrid.h"
#include "utcurvexyscale.h"
class UTCurveXYSetting : public QObject
{
    Q_OBJECT
public:
    explicit UTCurveXYSetting(QObject *parent = 0);
    
    UTCurveXYBorder &border(void);
    UTCurveXYGrid &grid(void);
    UTCurveXYScale &scale(void);
signals:

public slots:

private:
    UTCurveXYBorder *m_border;
    UTCurveXYGrid *m_grid;
    UTCurveXYScale *m_scale;
    
};

#endif // UTCURVEXYSETTING_H
