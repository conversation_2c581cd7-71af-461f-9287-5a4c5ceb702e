/*******************************************************************************
 *
 *修改记录
 * -# lihb 20140318 添加有点信息时获取来设置界面下拉框对应的量测类型和数据类型,
 *                  以便getCurveQueryInfo函数能正确获取;调整时间控件动作位置
 ******************************************************************************/


#include <QDebug>
#include <QCloseEvent>
#include "historydatadlg.h"
#include "ui_historydatadlg.h"
#include <QMessageBox>

/***********************
修改内容：增加检修历史查询
修改人：wuinqgixong
修改时间：20221213
***********************/


extern "C"
{
#include "mdbutypes.h"
//#if defined(SCADA_APP_VERSION)/*yzyadd version*/
#include "sca_define.h"
#include "scada_measdef.h"
#include "scadadb.h"
#include "sca_ext.h"
#include "scadamsg.h"
//#endif
}

#include "uiquery.h"
#include "gEntryDlg.h"
#include "utsysconfig.h"

HistoryDataDlg::HistoryDataDlg(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::HistoryDataDlg)
{
    ui->setupUi(this);
    setWindowFlags(windowFlags ()|Qt::WindowStaysOnTopHint);
    m_pid=0;
    //add 20231019 ckc
    m_queryid = 0;
    //end
    m_meastype=0;
    m_timetype=0;
    m_datatype=0;
    m_ycStatisticInfoDlg=NULL;
    m_trendCurveDlg=NULL;
    m_hisDataCopyDlg=NULL;
    m_hisDataEditDlg=NULL;
    m_entryDlg=NULL;
    m_pFaultSrcData=NULL;
    m_iFaultNum=0;
    m_iSelectFault=0;
    //add 20231024 ckc
    m_customYCCompleter = NULL;
    m_customYXCompleter = NULL;
    //end

    //历史数据
    //实时最大值
    //实时最小值
    //实时平均值
    //实时合格率
    //实时负荷率
    //统计最大值
    //统计最小值
    //统计平均值
    //统计合格率
    //统计负荷率
    //实时电度表码值//20131206yzyadd
    //存盘周期内累加
    m_datatypeDescList<<tr("His Data")
                      <<tr("Real Max Value")<<tr("Real Min Value")
                      <<tr("Real Average Value")<<tr("Real HGL Value")
                      <<tr("Real Pqrate Value")
                      <<tr("Stat Max Value")<<tr("Stat Min Value")
                      <<tr("Stat Average Value")<<tr("Stat HGL Value")
                      <<tr("Stat Pqrate Value")
                      <<tr("Kwh Meter Value")<<tr("Kwh Sum By Interval");
    m_datatypeIdList<<HIS_DATA_REAL
                    <<HIS_DATA_REAL_MAX<<HIS_DATA_REAL_MIN
                    <<HIS_DATA_REAL_AVERAGE<<HIS_DATA_REAL_HGL
                    <<HIS_DATA_REAL_PQRATE
                    <<HIS_DATA_STAT_MAX<<HIS_DATA_STAT_MIN
                    <<HIS_DATA_STAT_AVERAGE<<HIS_DATA_STAT_HGL
                    <<HIS_DATA_STAT_PQRATE
                    <<HIS_DATA_KWHCODE<<HIS_DATA_REAL;

    /*const char *anatypelabel[]={
        "有功功率","无功功率",
        "电压","电流",
        "档位","温度","功率因数","周波（频率)",
        "有功电度","无功电度","正向有功电度","负向有功电度","正向无功电度","负向无功电度",
        "A相有功","B相有功","C相有功","零序有功",
        "A相无功","B相无功","C相无功","零序无功",
        "A相电压","B相电压","C相电压","零序电压","AB线电压","BC线电压","CA线电压",
        "A相电流","B相电流","C相电流","零序电流"};*/

    m_ycTypeDescList<<tr("P")<<tr("Q")
                   <<tr("Voltage")<<tr("Current")
                  <<tr("Tap")<<tr("Temperature")
                 <<tr("Cos")<<tr("Hz")
                 <<tr("P Kwh")<<tr("Q Kwh")
                <<tr("Forward P Kwh")<<tr("Rev P Kwh")
               <<tr("Forward Q Kwh")<<tr("Rev Q Kwh")
                <<tr("A P")<<tr("B P")
               <<tr("C P")<<tr("0 P")
                <<tr("A Q")<<tr("B Q")
               <<tr("C Q")<<tr("0 Q")
                <<tr("A Vol")<<tr("B Vol")
               <<tr("C Vol")<<tr("0 Vol")
                <<tr("AB Vol")<<tr("BC Vol")
               <<tr("CA Vol")<<tr("A I")
              <<tr("B I")<<tr("C I")
             <<tr("0 I")
             <<tr("PLOSS")/* 功耗　　 */
             <<tr("EFFIDX")/* 能效指数　　 */
             <<tr("BEARLEVEL")/* 负载率　　 */
             <<tr("LOADLEVEL")/* 负荷率　　 */
             <<tr("VOLUB")/* 电压不平衡度　　 */
             <<tr("CRTUB")/* 电流不平衡度　　 */
             <<tr("UNITCONSUMER")/* 单位能耗　　 */
             <<tr("MDQUOTA")/* 最大需量(MD)配额　　 */
             <<tr("TOTCONSUMER")/* 总电量	*/
             <<tr("HIGHCONSUMER")/* 丰电量	*/
             <<tr("MEDCONSUMER")/* 平电量	*/
             <<tr("LOWCONSUMER")/* 谷电量	*/
             <<tr("TOTCOST")/* 总电费	*/
             <<tr("HIGHCOST")/* 丰电费	*/
             <<tr("MEDCOST")/* 平电费	*/
             <<tr("LOWCOST");/* 谷电费	*/
    m_ycTypeIdList<<ECS_K_MEASTYPE_NUM_P<<ECS_K_MEASTYPE_NUM_Q
                   <<ECS_K_MEASTYPE_NUM_U<<ECS_K_MEASTYPE_NUM_I
                   <<ECS_K_MEASTYPE_NUM_TAP<<ECS_K_MEASTYPE_NUM_TP
                   <<ECS_K_MEASTYPE_NUM_COS<<ECS_K_MEASTYPE_NUM_HZ
                   <<ECS_K_MEASTYPE_NUM_KWH<<ECS_K_MEASTYPE_NUM_MVAR
                   <<ECS_K_MEASTYPE_NUM_PKWH<<ECS_K_MEASTYPE_NUM_NKWH
                   <<ECS_K_MEASTYPE_NUM_PMVAR<<ECS_K_MEASTYPE_NUM_NMVAR
                   <<ECS_K_MEASTYPE_NUM_PA<<ECS_K_MEASTYPE_NUM_PB
                   <<ECS_K_MEASTYPE_NUM_PC<<ECS_K_MEASTYPE_NUM_PO
                   <<ECS_K_MEASTYPE_NUM_QA<<ECS_K_MEASTYPE_NUM_QB
                   <<ECS_K_MEASTYPE_NUM_QC<<ECS_K_MEASTYPE_NUM_QO
                   <<ECS_K_MEASTYPE_NUM_UA<<ECS_K_MEASTYPE_NUM_UB
                   <<ECS_K_MEASTYPE_NUM_UC<<ECS_K_MEASTYPE_NUM_UO
                   <<ECS_K_MEASTYPE_NUM_UAB<<ECS_K_MEASTYPE_NUM_UBC
                   <<ECS_K_MEASTYPE_NUM_UCA<<ECS_K_MEASTYPE_NUM_IA
                   <<ECS_K_MEASTYPE_NUM_IB<<ECS_K_MEASTYPE_NUM_IC
                   <<ECS_K_MEASTYPE_NUM_IO<<ECS_K_MEASTYPE_NUM_PLOSS
                   <<ECS_K_MEASTYPE_NUM_EFFIDX<<ECS_K_MEASTYPE_NUM_BEARLEVEL
                   <<ECS_K_MEASTYPE_NUM_BEARLEVEL<<ECS_K_MEASTYPE_NUM_LOADLEVEL
                   <<ECS_K_MEASTYPE_NUM_VOLUB<<ECS_K_MEASTYPE_NUM_UNITCONSUMER
                   <<ECS_K_MEASTYPE_NUM_MDQUOTA<<ECS_K_MEASTYPE_NUM_TOTCONSUMER
                   <<ECS_K_MEASTYPE_NUM_HIGHCONSUMER<<ECS_K_MEASTYPE_NUM_MEDCONSUMER
                   <<ECS_K_MEASTYPE_NUM_LOWCONSUMER<<ECS_K_MEASTYPE_NUM_TOTCOST
                   <<ECS_K_MEASTYPE_NUM_HIGHCOST<<ECS_K_MEASTYPE_NUM_MEDCOST
                   <<ECS_K_MEASTYPE_NUM_LOWCOST;

    /*static const char *digtypelabel[]=
    {
        "开关状态","刀闸状态",
        "保护信号","事故总信号",
        "一般量状态",
        "A相开关状态","B相开关状态","C相开关状态",
        "状态1","状态32"
    };*/
    m_yxTypeDescList<<tr("Breaker State")<<tr("Switch State")
                   <<tr("Protect Signal")<<tr("SGZ Signal")
                  <<tr("Normal State")<<tr("A Breaker State")
                 <<tr("B Breaker State")<<tr("C Breaker State")
                <<tr("State1")<<tr("State32");

    m_yxTypeIdList<<ECS_K_MEASTYPE_NUM_BRK<<ECS_K_MEASTYPE_NUM_SW
                  <<ECS_K_MEASTYPE_NUM_PROT<<ECS_K_MEASTYPE_NUM_SGZ
                  <<ECS_K_MEASTYPE_NUM_STA<<ECS_K_MEASTYPE_NUM_BRKA
                  <<ECS_K_MEASTYPE_NUM_BRKB<<ECS_K_MEASTYPE_NUM_BRKC
                  <<ECS_K_MEASTYPE_NUM_S1<<ECS_K_MEASTYPE_NUM_S32;

    /*static char* DeviceStatisticFlagDesc[]={
        "正常变位次数",
        "事故变位次数",
        "遥控次数（升降次数）",
        "变化为开状态的次数",
        "遥控拒动次数",
        "变化为合状态的次数",
        "变化为中间态00的次数",
        "变化为中间态11的次数",
        "处于合状态的持续时间",
        "处于中间态00的持续时间",
        "处于中间态11的持续时间"
    };*/
    m_yxStatisDescList<<tr("Normal change times")
                     <<tr("Fault change times")
                     <<tr("YK times(YT times)")
                     <<tr("Change to open times")
                     <<tr("YK refuse change times")
                     <<tr("change to close times")
                     <<tr("change to 00 times")
                     <<tr("change to 11 times")
                     <<tr("close time")
                     <<tr("00 time")
                     <<tr("11 time");
    m_yxStatisIdList<<HIS_DEV_STAT_VALUE1
                    <<HIS_DEV_STAT_VALUE2
                    <<HIS_DEV_STAT_VALUE3
                    <<HIS_DEV_STAT_VALUE4
                    <<HIS_DEV_STAT_VALUE21
                    <<HIS_DEV_STAT_VALUE5
                    <<HIS_DEV_STAT_VALUE6
                    <<HIS_DEV_STAT_VALUE7
                    <<HIS_DEV_STAT_VALUE10
                    <<HIS_DEV_STAT_VALUE11
                    <<HIS_DEV_STAT_VALUE12;

    /*static char* RtuStatisticFlagDesc[]={
        "RTU运行时间",
        "RTU故障时间",
        "RTU停止次数"
    };*/
    m_rtuStatisDescList<<tr("Rtu run time")
                       <<tr("Rtu falut time")
                       <<tr("Rtu stop times");
    m_rtuStatisIdList<<HIS_DEV_STAT_VALUE1
                     <<HIS_DEV_STAT_VALUE2
                     <<HIS_DEV_STAT_VALUE7;

    /*static char* RouteStatisticFlagDesc[]={
        "通道运行时间",
        "通道停止次数"
    };
    */
    m_channelStatisDescList<<tr("Channel run time")
                           <<tr("Channel stop times");
    m_channelStatisIdList<<HIS_DEV_RUNTIME
                         <<HIS_DEV_STAT_VALUE1;

    m_headLabels.append(tr("Statistic Item"));
    m_headLabels.append(tr("Statistic Value"));


    setupModel();

    initCompleter();    //需要在初始化厂站之前调用 add 20231024 ckc

    //add by qianwx 20211229
    initManualDataUi();

    InitialJXQueryControl();//20221213 wuqingxiong add

    setupChart();

//    initMeasType();

//    initTimeType();
//    TimeTypeActivatedSlot(0);

    ui->dateEdit_Normal->setDisplayFormat("yyyy-M-d");
    ui->dateTimeEdit_Begin->setDisplayFormat("yyyy-M-d H:mm");
    ui->dateTimeEdit_Begin->setDate(QDate::currentDate());//.addDays(-2));
    ui->dateTimeEdit_End->setDisplayFormat("yyyy-M-d H:mm");
    ui->dateTimeEdit_End->setDate(QDate::currentDate().addDays(1));
    ui->dateEdit_YXNormal->setDisplayFormat("yyyy-M-d");
    ui->dateEdit_RtuNormal->setDisplayFormat("yyyy-M-d");
    ui->dateEdit_ChannelNormal->setDisplayFormat("yyyy-M-d");
    ui->dateTimeEdit_ReCallBegin->setDisplayFormat("yyyy-M-d H:mm");
    ui->dateTimeEdit_ReCallEnd->setDisplayFormat("yyyy-M-d H:mm");
    //yzytest 设置测试数据时间
    //ui->dateEdit_Normal->setDate(QDate(2013,2,21));

    connect(m_tablemodel, SIGNAL(dataChanged ( const QModelIndex &, const QModelIndex & )),
            this, SLOT(dataChangedSlot ( const QModelIndex &, const QModelIndex & )));
    connect(ui->tableView_ReCallSource, SIGNAL(clicked ( const QModelIndex & )),
            this, SLOT(tableClickedSlot ( const QModelIndex & )));
    connect(ui->comboBox_YCArea, SIGNAL(activated(int)),
            this, SLOT(AreaActivatedSlot(int)) );
    connect(ui->comboBox_speedArea, SIGNAL(activated(int)),
            this, SLOT(speedAreaActivatedSlot(int)));
    connect(ui->comboBox_YXArea, SIGNAL(activated(int)),
            this, SLOT(YXAreaActivatedSlot(int)) );
    connect(ui->comboBox_RtuArea, SIGNAL(activated(int)),
            this, SLOT(RtuAreaActivatedSlot(int)) );
    connect(ui->comboBox_ChannelArea, SIGNAL(activated(int)),
            this, SLOT(ChannelAreaActivatedSlot(int)) );
    connect(ui->comboBox_ReCallArea, SIGNAL(activated(int)),
            this, SLOT(ReCallAreaActivatedSlot(int)) );
    connect(ui->comboBox_YCStation, SIGNAL(activated(int)),
            this, SLOT(YCStationActivatedSlot(int)) );
    connect(ui->comboBox_YXStation, SIGNAL(activated(int)),
            this, SLOT(YXStationActivatedSlot(int)) );
    connect(ui->comboBox_RtuStation, SIGNAL(activated(int)),
            this, SLOT(RtuStationActivatedSlot(int)) );
    connect(ui->comboBox_ChannelStation, SIGNAL(activated(int)),
            this, SLOT(ChannelStationActivatedSlot(int)) );
    connect(ui->comboBox_ReCallStation, SIGNAL(activated(int)),
            this, SLOT(ReCallStationActivatedSlot(int)) );
    connect(ui->comboBox_ReCallYXYC, SIGNAL(activated(int)),
            this, SLOT(YXYCTypeActivatedSlot(int)) );
    connect(ui->comboBox_YCMeasType, SIGNAL(activated(int)),
            this, SLOT(YCMeasTypeActivatedSlot(int)) );
    connect(ui->comboBox_YXMeasType, SIGNAL(activated(int)),
            this, SLOT(YXMeasTypeActivatedSlot(int)) );
    connect(ui->comboBox_ReCallMeasType, SIGNAL(activated(int)),
            this, SLOT(ReCallMeasTypeActivatedSlot(int)) );
    connect(ui->comboBox_TimeType, SIGNAL(activated(int)),
            this, SLOT(TimeTypeActivatedSlot(int)) );
    connect(ui->pushButton_Add, SIGNAL(clicked()),
            this, SLOT(addCurveSlot()) );
    connect(ui->pushButton_Delete, SIGNAL(clicked()),
            this, SLOT(deleteCurveSlot()) );
    connect(ui->pushButton_Load, SIGNAL(clicked()),
            this, SLOT(queryCurveDataSlot()) );
    connect(ui->listWidget_Curve, SIGNAL(itemSelectionChanged()),
            this, SLOT(CurveItemSelectionChangedSlot()) );
    connect(ui->pushButton_PreDay, SIGNAL(clicked()),
            this, SLOT(preDayCurveSlot()) );
    connect(ui->pushButton_NextDay, SIGNAL(clicked()),
            this, SLOT(nextDayCurveSlot()) );
    connect(ui->pushButton_StatisData, SIGNAL(clicked()),
            this, SLOT(staisDataInfoSlot()) );
    connect(ui->pushButton_YXStatisData, SIGNAL(clicked()),
            this, SLOT(staisYXDataInfoSlot()) );
    connect(ui->pushButton_RtuStatisData, SIGNAL(clicked()),
            this, SLOT(staisRtuDataInfoSlot()) );
    connect(ui->pushButton_ChannelStatisData, SIGNAL(clicked()),
            this, SLOT(staisChannelDataInfoSlot()) );
    connect(ui->pushButton_RealDayStatis, SIGNAL(clicked()),
            this, SLOT(realDayStatisSlot()) );
    connect(ui->pushButton_RealMonStatis, SIGNAL(clicked()),
            this, SLOT(realMonStatisSlot()) );
    connect(ui->pushButton_RealYearStatis, SIGNAL(clicked()),
            this, SLOT(realYearStatisSlot()) );
    connect(ui->pushButton_HisDayStatis, SIGNAL(clicked()),
            this, SLOT(hisDayStatisSlot()) );
    connect(ui->pushButton_YXHisDayStatis, SIGNAL(clicked()),
            this, SLOT(hisDayStatisSlot()) );
    connect(ui->pushButton_HisMonStatis, SIGNAL(clicked()),
            this, SLOT(hisMonStatisSlot()) );
    connect(ui->pushButton_YXHisMonStatis, SIGNAL(clicked()),
            this, SLOT(hisMonStatisSlot()) );
    connect(ui->pushButton_RtuHisMonStatis, SIGNAL(clicked()),
            this, SLOT(hisMonStatisSlot()) );
    connect(ui->pushButton_ChannelHisMonStatis, SIGNAL(clicked()),
            this, SLOT(hisMonStatisSlot()) );
    connect(ui->pushButton_HisYearStatis, SIGNAL(clicked()),
            this, SLOT(hisYearStatisSlot()) );
    connect(ui->pushButton_YXHisYearStatis, SIGNAL(clicked()),
            this, SLOT(hisYearStatisSlot()) );
    connect(ui->pushButton_RtuHisYearStatis, SIGNAL(clicked()),
            this, SLOT(hisYearStatisSlot()) );
    connect(ui->pushButton_ChannelHisYearStatis, SIGNAL(clicked()),
            this, SLOT(hisYearStatisSlot()) );
    connect(ui->pushButton_RealTrend, SIGNAL(clicked()),
            this, SLOT(realTrendCurveSlot()) );
    connect(ui->pushButton_Close, SIGNAL(clicked()),
            this, SLOT(closeSlot()) );
    connect(ui->pushButton_YXClose, SIGNAL(clicked()),
            this, SLOT(closeSlot()) );
    connect(ui->pushButton_RtuClose, SIGNAL(clicked()),
            this, SLOT(closeSlot()) );
    connect(ui->pushButton_ChannelClose, SIGNAL(clicked()),
            this, SLOT(closeSlot()) );
    connect(ui->pushButton_ReCallClose, SIGNAL(clicked()),
            this, SLOT(closeSlot()) );
    connect(ui->pushButton_CopyData, SIGNAL(clicked()),
            this, SLOT(copyHisDataSlot()) );
    connect(ui->pushButton_EditData, SIGNAL(clicked()),
            this, SLOT(editHisDataSlot()) );
    connect(ui->pushButton_Print, SIGNAL(clicked()),
            this, SLOT(printCurveSlot()) );
    connect(ui->pushButton_ExportTable, SIGNAL(clicked()),
            this, SLOT(exportTableSlot()) );//2151221yzy
    connect(ui->pushButton_ReCallLoad, SIGNAL(clicked()),
            this, SLOT(selectFaultSourceSlot()) );
    connect(ui->pushButton_ReCallDelete, SIGNAL(clicked()),
            this, SLOT(deletFaultSlot()) );
    connect(ui->pushButton_ReCallLoadData, SIGNAL(clicked()),
            this, SLOT(loadFaultInfoSlot()) );
    connect(ui->pushButton_find, SIGNAL(clicked()),
            this, SLOT(findYcPointSlot()) );
    connect(ui->pushButton_find_YX, SIGNAL(clicked()),
            this, SLOT(findYxPointSlot()) );
    //********
    connect(ui->checkBox_ThirdCXCurve,SIGNAL(stateChanged ( int  )),this,SLOT(thirdCZCurveCheckedSlot(int)) );
    ui->pushButton_YSet->setVisible(false);
    bool plcbankflag=false;
    QString isNshversion=UtSysConfig::getSysConfigValueByKey("plc", "plc_bank_version");
    if (!isNshversion.isEmpty() && isNshversion =="1")
    {
        plcbankflag=true;
    }

    //读取配置的窗口位置和大小
    m_widget_rect=UtSysConfig::UtGetQSettingsValueByKey("hisdman",
                                                         "hisdmandlg",
                                                         "rect").toRect();

    if( m_widget_rect.width()>100 && m_widget_rect.height()>100)
    {
        //resize(widget_rect.width(),widget_rect.height());
        if(plcbankflag)
            this->setGeometry(m_widget_rect);
        else
            resize(m_widget_rect.width(),m_widget_rect.height());
    }
    else
    {
        m_widget_rect=QRect(x(),y(),width(),height());
    }
}

HistoryDataDlg::~HistoryDataDlg()
{
    delete ui;
}

void  HistoryDataDlg::closeEvent(QCloseEvent *event)
{
    if(m_widget_rect!=QRect(x(),y(),width(),height()))
    {
        UtSysConfig::UtSaveQSettingsValueByKey("hisdman",
                                               "hisdmandlg",
                                               "rect",QRect(x(),y(),width(),height()));
        m_widget_rect=QRect(x(),y(),width(),height());
    //qDebug()<<"save m_widget_rect="<<QRect(x(),y(),width(),height());
    }
    event->accept();
}

//设置CDA会话句柄
void HistoryDataDlg::setCDASessionHandle(CDA_SES_HANDLE handle)
{
    m_cdaSessionHandle=handle;
}

//显示对话框
void HistoryDataDlg::showDlg()
{
    this->showMaximized();
    initMeasType();
    initTimeType();
    TimeTypeActivatedSlot(0);


    initYCTab();
    initYXTab();
    initRtuTab();
    initChannelTab();
    initReCallTab();
    initspeedTab();
    loadManStatData();
    ui->tableView_speed->setVisible(false);
    //20210430 yzy 为开普测试增加是否允许修改的环境变量,默认不允许修改
    QString flagstr=qgetenv("HISDMAN_EDIT_HISDATA_FLAG");
    if(flagstr.isEmpty()||flagstr.toUpper()=="FALSE"||flagstr=="0")
    {
        ui->pushButton_EditData->setVisible(false);
        ui->pushButton_CopyData->setVisible(false);
        ui->pushButton_ReCallDelete->setVisible(false);
    }
    ///

    if(m_entryDlg&&m_entryDlg->QueryPerm(MMI_USR_EDIT_HISDATA))
    {
        ui->pushButton_EditData->setEnabled(true);
        ui->pushButton_CopyData->setEnabled(true);
        ui->pushButton_StatisData->setEnabled(true);
        ui->pushButton_ReCallDelete->setEnabled(true);
    }
    else
    {
        ui->pushButton_EditData->setEnabled(false);
        ui->pushButton_CopyData->setEnabled(false);
        ui->pushButton_StatisData->setEnabled(false);
        ui->pushButton_ReCallDelete->setEnabled(false);
    }
    int i;
    //******** yzy 删除通道统计tab页
    if(ui->tabWidget->count()>3)
        ui->tabWidget->removeTab(3);

    /*start:******** wuqingxiong add 删除rtu统计tab页*/
    if(ui->tabWidget->count()>2)
        ui->tabWidget->removeTab(2);
    /*end:******** wuqingxiong add*/

    //判断是否显示其他tab页
    QString str=UtSysConfig::getSysConfigValueByKey("plc","plc_bank_version");
    if(!str.isEmpty()&&str.toInt()==1)
    {
        for(i=ui->tabWidget->count()-1;i>=1;i--)
        {
            ui->tabWidget->removeTab(i);
        }
    }
    showNormal();
    activateWindow();
    qDebug()<<"m_pid"<<m_pid;
    if(m_pid>0)
    {
        //设置界面的值
        ui->comboBox_TimeType->setCurrentIndex(0);

        sint32 PidIdx;
        PidIdx=SCADA_PidToIdx[m_pid];
        int saveinterval=0;
        uint32 mtype=0;
        //qDebug()<<"PidIdx"<<PidIdx
        //       <<"SCADA_PointDescCat->extent"<<SCADA_PointDescCat->extent;
        if(PidIdx>0&&PidIdx<=SCADA_PointDescCat->extent)
        {
            //qDebug()<<"SCADA_PointSave[PidIdx].save_interval"<<SCADA_PointSave[PidIdx].save_interval;
            saveinterval=SCADA_PointSave[PidIdx].save_interval*60;
            mtype=SCADA_PointDev[PidIdx].meastype;
        }

        //lihbdebug 添加点信息的获取来设置界面下拉框对应的量测类型和数据类型,以便getCurveQueryInfo函数能正确获取
//        PointInfo *pInfo = MMI_GetPointInfoFromPointId(m_pid);
//        if(pInfo)
        {
            QVariant val;

//            uint32 mtype = pInfo->mtype;
            for(i=0;i<ui->comboBox_YCMeasType->count();i++)
            {
                val=ui->comboBox_YCMeasType->itemData(i);
                if( mtype==val.toInt() )
                {
                    ui->comboBox_YCMeasType->setCurrentIndex(i);
                    m_meastype = mtype;//使用点的量测类型
                    initDataType();//刷新相应的数据类型,能设置到想要的查询的数据类型吗?
                    break;
                }
            }

            //默认是 HIS_DATA_REAL 恰好HIS_DATA_REAL 就在第一个下拉项，
            //如果不是第一个下拉项，则需要遍历后设置了
            /*****
            uint32 defaultHisType = HIS_DATA_REAL;
            for(i=0;i<ui->comboBox_DataType->count();i++)
            {
                val=ui->comboBox_DataType->itemData(i);
                if( defaultHisType==val.toInt() )
                {
                    ui->comboBox_DataType->setCurrentIndex(i);
                    break;
                }
            }
            ********/
        }
        //lihbadd end

        getCurveQueryInfo();

        if(saveinterval<1)
            return;
        addOneCurve(m_pid,start_datetime,end_datetime,saveinterval);
        //add 20231019 ckc
        m_queryid = m_pid;
        //end
    }
}

void HistoryDataDlg::initspeedTab()
{
    ui->comboBox_speedArea->clear();
    ui->comboBox_speedStation->clear();
    ui->comboBox_speedMeasType->clear();

    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;

    int i=0;
    int pos=0;
    //区域
    for(i=1;i<=SCADA_AreaCat->extent;i++)
    {
        if(SCADA_Area[i].areaId==0)
            continue;
        ui->comboBox_speedArea->addItem(QString::fromLocal8Bit(SCADA_Area[i].area_desc));
        ui->comboBox_speedArea->setItemData(pos,SCADA_Area[i].areaId);
        pos++;
    }

    if(ui->comboBox_speedArea->count()>0)
        AreaActivatedSlot(ui->comboBox_speedArea->currentIndex());
}

//初始遥测Tab
void HistoryDataDlg::initYCTab()
{
    ui->comboBox_YCArea->clear();
    ui->comboBox_YCStation->clear();
    ui->dateEdit_Normal->setDate(QDate::currentDate());

    //调整时间控件动作位置
    ui->dateEdit_Normal->setSelectedSection(QDateTimeEdit::DaySection);

    //初始化区域
    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;
    int i=0;
    int pos=0;
    for(i=1;i<=SCADA_AreaCat->extent;i++)
    {
        if(SCADA_Area[i].areaId==0)
            continue;
        ui->comboBox_YCArea->addItem(QString::fromLocal8Bit(SCADA_Area[i].area_desc));
        ui->comboBox_YCArea->setItemData(pos,SCADA_Area[i].areaId);
        pos++;
    }
    if(ui->comboBox_YCArea->count()>0)
        AreaActivatedSlot(ui->comboBox_YCArea->currentIndex());
}

void getAllMeasTypeInfo(CDA_SES_HANDLE handle,
                        QList<QString>&yxdesclist,
                        QList<int> &yxtypelist,
                        QList<QString>&ycdesclist,
                        QList<int> &yctypelist
                        )
{
    void* resBuf = NULL;
    int   resRowSize = 0, resNum = 0;
    MSG_ID_T status;
    int i=0;

    UICDAReq *uiReq = new UICDAReq(handle);
    status = uiReq->setCDAPlan(UICDA_SELECT_RECORD, CDASynMode);

    status = uiReq->setReadAttr("MT_MeasTypeNo");
    status = uiReq->setReadAttr("MT_MeasType");
    status = uiReq->setReadAttr("MT_MeasTypeClass");
    status = uiReq->setCrit("MT_MeasTypeId",CDA_M_ALL,NULL);


    status = uiReq->selectRcd(&resBuf, resRowSize, resNum);
    printf(" the resNum == %d \n", resNum);

    if(status != UI__NORMAL) {
        printf("getAllMeasTypeInfo: selectRcd failed! status = %d\n", status);
        if(uiReq) {delete uiReq;uiReq = NULL;}
        if(resBuf) {free(resBuf);resBuf = NULL;}
        return;
    }
    if( resNum==0) {
        if(uiReq) {delete uiReq; uiReq = NULL;}
        if(resBuf) {free(resBuf);resBuf = NULL;}
        return;
    }

    QChar ccharstr;
    int type=0;
    for(i=0;i<resNum;i++)
    {
        type=(int)*(uint32*)(uiReq->getValue(i, 0));
        ccharstr =(char)*(uint32*)(uiReq->getValue(i,2));
       // if(type<700||type>1200)
       // B保护 A遥测 K是电度量 S遥控的设置值  C遥控的控制  
        if (ccharstr == 'A' || ccharstr == 'K' ) 
        {
            ycdesclist<<QString::fromLocal8Bit((char*)(uiReq->getValue(i,1)));
            yctypelist<<(int)*(uint32*)(uiReq->getValue(i, 0));
        }
        else if (ccharstr == 'D' || ccharstr == 'B')
        {
            //qDebug()<<"str=="<<str<<" desc=="<<QString::fromLocal8Bit((char*)(uiReq->getValue(i,1)));
            yxdesclist<<QString::fromLocal8Bit((char*)(uiReq->getValue(i,1)));
            yxtypelist<<(int)*(uint32*)(uiReq->getValue(i, 0));
        }
    }
    if(uiReq)  { delete uiReq; uiReq=NULL; }
    if(resBuf) { free(resBuf); resBuf=NULL; }
}

//初始化量测类型
void HistoryDataDlg::initMeasType()
{
    //初始化区域
    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;

    int i=0;
    //20161220遍历meastype表读取遥信遥测
    m_ycTypeDescList.clear();
    m_ycTypeIdList.clear();
    m_yxTypeDescList.clear();
    m_yxTypeIdList.clear();
    getAllMeasTypeInfo(m_cdaSessionHandle,m_yxTypeDescList,m_yxTypeIdList,
                       m_ycTypeDescList,m_ycTypeIdList);



    ui->comboBox_YCMeasType->clear();
    int pos=0;
    for(i=0;i<m_ycTypeDescList.count();i++)
    {
        ui->comboBox_YCMeasType->addItem(m_ycTypeDescList.at(i));
        ui->comboBox_YCMeasType->setItemData(pos,m_ycTypeIdList.at(i));
        pos++;
    }
    /*for(i=1;i<=SCADA_MeasTypeCat->extent;i++)
    {
        if(SCADA_MeasType[i].meas_type_id==0)
            continue;
        for(j=0;j<num;j++)
        {
            if(SCADA_MeasType[i].meas_type_id==anatypecode[j])
            {
                ui->comboBox_YCMeasType->addItem(QString::fromLocal8Bit(SCADA_MeasType[i].meas_type_desc));
                ui->comboBox_YCMeasType->setItemData(pos,anatypecode[j]);
                pos++;
                break;
            }
        }
    }*/
}

//初始化查询时间类型
void HistoryDataDlg::initTimeType()
{
    ui->comboBox_TimeType->addItem(tr("Day"));
    ui->comboBox_TimeType->setItemData(0,UTCurve_DayCurve);
    ui->comboBox_TimeType->addItem(tr("Month"));
    ui->comboBox_TimeType->setItemData(1,UTCurve_MonCurve);
    ui->comboBox_TimeType->addItem(tr("Year"));
    ui->comboBox_TimeType->setItemData(2,UTCurve_YearCurve);
    ui->comboBox_TimeType->addItem(tr("TimeToTime"));
    ui->comboBox_TimeType->setItemData(3,UTCurve_TimeToTimeCurve);
}

//查询时间类型选择
void HistoryDataDlg::TimeTypeActivatedSlot(int index)
{
    if(index==3)//如果选择时段则设置控件显示
    {
        ui->label_Normal->setVisible(false);
        ui->dateEdit_Normal->setVisible(false);
        ui->label_TimeTime->setVisible(true);
        ui->dateTimeEdit_Begin->setVisible(true);
        ui->label_To->setVisible(true);
        ui->dateTimeEdit_End->setVisible(true);
    }
    else
    {
        ui->label_Normal->setVisible(true);
        ui->dateEdit_Normal->setVisible(true);
        ui->label_TimeTime->setVisible(false);
        ui->dateTimeEdit_Begin->setVisible(false);
        ui->label_To->setVisible(false);
        ui->dateTimeEdit_End->setVisible(false);
    }
    switch(index)
    {
    case 0://日
        ui->curve->setting().grid().horizzontalTick().setTickMajor(13);
        ui->curve->setting().grid().horizzontalTick().setTickMinor(6);
        ui->curve->datesetting().setCurveType(UTCurve_DayCurve);

        //调整时间控件动作位置
        ui->dateEdit_Normal->setSelectedSection(QDateTimeEdit::DaySection);

        break;
    case 1://月
        ui->curve->setting().grid().horizzontalTick().setTickMajor(16);
        ui->curve->setting().grid().horizzontalTick().setTickMinor(2);
        ui->curve->datesetting().setCurveType(UTCurve_MonCurve);

        //调整时间控件动作位置
        ui->dateEdit_Normal->setSelectedSection(QDateTimeEdit::MonthSection);

        break;
    case 2://年
        ui->curve->setting().grid().horizzontalTick().setTickMajor(13);
        ui->curve->setting().grid().horizzontalTick().setTickMinor(2);
        ui->curve->datesetting().setCurveType(UTCurve_YearCurve);

        //调整时间控件动作位置
        ui->dateEdit_Normal->setSelectedSection(QDateTimeEdit::YearSection);

        break;
    case 3://时段
        ui->curve->setting().grid().horizzontalTick().setTickMajor(13);
        ui->curve->setting().grid().horizzontalTick().setTickMinor(6);
        ui->curve->datesetting().setCurveType(UTCurve_TimeToTimeCurve);

        //调整时间控件动作位置
        ui->dateTimeEdit_Begin->setSelectedSection(QDateTimeEdit::DaySection);
        ui->dateTimeEdit_End->setSelectedSection(QDateTimeEdit::DaySection);

        break;
    default:
        break;
    }

    if(index==0)//如果选择日数据
    {
        ui->pushButton_PreDay->setEnabled(true);
        ui->pushButton_NextDay->setEnabled(true);
    }
    else
    {
        ui->pushButton_PreDay->setEnabled(false);
        ui->pushButton_NextDay->setEnabled(false);
    }
    if(index==3)//时段20160704
    {
        ui->pushButton_EditData->setEnabled(false);
    }
    else
    {
        ui->pushButton_EditData->setEnabled(true);
    }
    initDataType();
    //edit 20231019 ckc
    ui->listWidget_Curve->clear();
    ui->curve->datesetting().clearAllCurve();
    ui->curve->update();
    m_tablemodel->setColumnCount(0);
    m_tablemodel->setRowCount(0);
    m_dataTableTimemarkList.clear();
    qDebug() << "=====m_queryid = " << m_queryid << m_pid << endl;
    if (m_queryid > 0)
    {
        sint32 PidIdx;
        PidIdx = SCADA_PidToIdx[m_queryid];
        int saveinterval = 0;
        uint32 mtype = 0;
        if (PidIdx > 0 && PidIdx <= static_cast<sint32>(SCADA_PointDescCat->extent))
        {
            saveinterval = SCADA_PointSave[PidIdx].save_interval * 60;
            mtype = SCADA_PointDev[PidIdx].meastype;
        }
        getCurveQueryInfo();

        if(saveinterval<1)
            return;
        addOneCurve(m_queryid, start_datetime, end_datetime, saveinterval);

    }
    //end
    //add 20231019 ckc
//    if (ui->listWidget_Curve->selectedItems().size() > 0)
//    {
//        queryCurveDataSlot();
//    }
    //end
}

//初始查询数据类型
void HistoryDataDlg::initDataType()
{
    ui->comboBox_DataType->clear();
    //根据选择的查询时间类型进行判断
    QVariant val;
    int i=0;
    int pos=0;
    val=ui->comboBox_TimeType->itemData(ui->comboBox_TimeType->currentIndex());
    if(val.toInt()==UTCurve_DayCurve
       ||val.toInt()==UTCurve_TimeToTimeCurve)//日曲线和时段曲线只有历史数据
    {
        //20131206如果是电度则区分表码值和存盘间隔累加值
        if( m_meastype > ECS_K_MEASTYPE_NUM_KWH-1 &&
                m_meastype < ECS_K_MEASTYPE_NUM_NMVAR+1 )
        {
            for(i=m_datatypeDescList.count()-2;i<m_datatypeDescList.count();i++)
            {
                ui->comboBox_DataType->addItem(m_datatypeDescList.at(i));
                ui->comboBox_DataType->setItemData(pos,m_datatypeIdList.at(i));
                pos++;
            }
        }
        else
        {
            //实时值
            ui->comboBox_DataType->addItem(m_datatypeDescList.at(0));
            ui->comboBox_DataType->setItemData(0,m_datatypeIdList.at(0));
        }
    }
    else if(val.toInt()==UTCurve_MonCurve)//月曲线
    {
        for(i=1;i<m_datatypeDescList.count();i++)
        {
            ui->comboBox_DataType->addItem(m_datatypeDescList.at(i));
            ui->comboBox_DataType->setItemData(pos,m_datatypeIdList.at(i));
            pos++;
        }
    }
    else //年曲线，只有统计值
    {
        for(i=6;i<m_datatypeDescList.count();i++)
        {
            ui->comboBox_DataType->addItem(m_datatypeDescList.at(i));
            ui->comboBox_DataType->setItemData(pos,m_datatypeIdList.at(i));
            pos++;
        }
    }
}

void HistoryDataDlg::speedAreaActivatedSlot(int index)
{
    ui->comboBox_speedStation->clear();
    QVariant val;
    val=ui->comboBox_speedArea->itemData(index);
    int areaid=val.toInt();

    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;

    int i=0;
    int pos=0;
    //刷新厂站
    for(i=1;i<=SCADA_StationCat->extent;i++)
    {
        if(SCADA_Station[i].station_no==0
                ||(areaid!=1&&areaid!=SCADA_Station[i].area_idx))
            continue;
        ui->comboBox_speedStation->addItem(QString::fromLocal8Bit(SCADA_Station[i].station_desc));
        ui->comboBox_speedStation->setItemData(pos,SCADA_Station[i].station_no);
        pos++;
    }
    fillspeedPointList();
}

//区域选择
void HistoryDataDlg::AreaActivatedSlot(int index)
{
    ui->comboBox_YCStation->clear();
    QVariant val;
    val=ui->comboBox_YCArea->itemData(index);
    int areaid=val.toInt();
    //刷新厂站
    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;
    int i=0;
    int pos=0;
    m_customYCCompleter->SetBlockSignal(true);         //add 20231024 ckc
    QStringList matchList;      //厂站全匹配列表，用以搜索匹配
    for(i=1;i<=SCADA_StationCat->extent;i++)
    {
        if(SCADA_Station[i].station_no==0
                ||(areaid!=1&&areaid!=SCADA_Station[i].area_idx))
            continue;
        ui->comboBox_YCStation->addItem(QString::fromLocal8Bit(SCADA_Station[i].station_desc));
        ui->comboBox_YCStation->setItemData(pos,SCADA_Station[i].station_no);
        matchList.append(QString::fromLocal8Bit(SCADA_Station[i].station_desc));
        pos++;
    }
    if (!m_customYCCompleter)
    {
        initCompleter();
    }
    m_customYCCompleter->SetMatchList(matchList);
    m_customYCCompleter->SetBlockSignal(false);          //add 20231024 ckc
    fillPointList();
}

//厂站选择
void HistoryDataDlg::YCStationActivatedSlot(int index)
{
    fillPointList();
}

//量测类型选择
void HistoryDataDlg::YCMeasTypeActivatedSlot(int index)
{
    fillPointList();
    initDataType();//20131206
}

void HistoryDataDlg::fillspeedPointList()
{
    ui->listWidget_speedPoint->clear();
    if(ui->comboBox_speedStation->count()<1
       ||ui->comboBox_speedArea->count()<1)
    {
        return;
    }
    QVariant val;
    int staid=0;
    int meastypeid=0;
    val=ui->comboBox_speedStation->itemData(ui->comboBox_speedStation->currentIndex());
    staid=val.toInt();
    val=ui->comboBox_speedMeasType->itemData(ui->comboBox_speedMeasType->currentIndex());
    meastypeid=val.toInt();
    if(staid<1||meastypeid<1)
        return;
    m_meastype=meastypeid;//20131206
    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;
    int i=0;
    int pos=0;
    QListWidgetItem *item=NULL;
    for(i=1;i<=SCADA_PointDevCat->extent;i++)
    {
        if(SCADA_PointType[i].station_idx!=staid
           ||SCADA_PointDev[i].meastype!=meastypeid )//大于0小于600 遥测
        {
            continue;
        }
        ui->listWidget_YCPoint->addItem( QString::fromLocal8Bit(SCADA_PointDesc[i].point_desc) );
        item=ui->listWidget_YCPoint->item(pos);
        item->setData(Item_UserRole_Pid,SCADA_PointType[i].pid);
        //qDebug()<<"SCADA_PointSave[i].save_interval"<<SCADA_PointSave[i].save_interval;
        item->setData(Item_UserRole_SaveInterval,SCADA_PointSave[i].save_interval);
        pos++;
    }
}

//根据厂站和量测类型刷新点列表
void HistoryDataDlg::fillPointList()
{
    ui->listWidget_YCPoint->clear();
    if(ui->comboBox_YCStation->count()<1
       ||ui->comboBox_YCArea->count()<1)
    {
        return;
    }
    QVariant val;
    int staid=0;
    int meastypeid=0;
    val=ui->comboBox_YCStation->itemData(ui->comboBox_YCStation->currentIndex());
    staid=val.toInt();
    val=ui->comboBox_YCMeasType->itemData(ui->comboBox_YCMeasType->currentIndex());
    meastypeid=val.toInt();
    if(staid<1||meastypeid<1)
        return;
    m_meastype=meastypeid;//20131206
    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;
    int i=0;
    int pos=0;
    QListWidgetItem *item=NULL;
    for(i=1;i<=SCADA_PointDevCat->extent;i++)
    {

        if(SCADA_PointType[i].station_idx!=staid
           ||SCADA_PointDev[i].meastype!=meastypeid)//大于0小于600 遥测
        {
            continue;
        }
        ui->listWidget_YCPoint->addItem( QString::fromLocal8Bit(SCADA_PointDesc[i].point_desc) );
        item=ui->listWidget_YCPoint->item(pos);
        item->setData(Item_UserRole_Pid,SCADA_PointType[i].pid);
        //qDebug()<<"SCADA_PointSave[i].save_interval"<<SCADA_PointSave[i].save_interval;
        item->setData(Item_UserRole_SaveInterval,SCADA_PointSave[i].save_interval);
        pos++;
    }
}

void HistoryDataDlg::setupModel(void)
{
    int i;
    qreal val=0;
    QDateTime date_time=QDateTime::currentDateTimeUtc();
    date_time=QDateTime(date_time.date());

    m_tablemodel=new QStandardItemModel(this);
    ui->tableView->setModel(m_tablemodel);

    m_yxtablemodel=new QStandardItemModel(this);
    m_yxtablemodel->setColumnCount(m_headLabels.count());
    m_yxtablemodel->setHorizontalHeaderLabels(m_headLabels);
    ui->tableView_YX->setModel(m_yxtablemodel);
    ui->tableView_YX->setSelectionBehavior( QAbstractItemView::SelectRows );
    ui->tableView_YX->setEditTriggers(QAbstractItemView::NoEditTriggers);//20111026 yzy设置表格为只读

    m_rtutablemodel=new QStandardItemModel(this);
    m_rtutablemodel->setColumnCount(m_headLabels.count());
    m_rtutablemodel->setHorizontalHeaderLabels(m_headLabels);
    ui->tableView_Rtu->setModel(m_rtutablemodel);
    ui->tableView_Rtu->setSelectionBehavior( QAbstractItemView::SelectRows );
    ui->tableView_Rtu->setEditTriggers(QAbstractItemView::NoEditTriggers);//20111026 yzy设置表格为只读

    m_channeltablemodel=new QStandardItemModel(this);
    m_channeltablemodel->setColumnCount(m_headLabels.count());
    m_channeltablemodel->setHorizontalHeaderLabels(m_headLabels);
    ui->tableView_Channel->setModel(m_channeltablemodel);
    ui->tableView_Channel->setSelectionBehavior( QAbstractItemView::SelectRows );
    ui->tableView_Channel->setEditTriggers(QAbstractItemView::NoEditTriggers);//20111026 yzy设置表格为只读

    QStringList headLabels;
    headLabels.append(tr("Fault Time"));
    headLabels.append(tr("Fault Info"));
    m_recallsrctablemodel=new QStandardItemModel(this);
    m_recallsrctablemodel->setColumnCount(headLabels.count());
    m_recallsrctablemodel->setHorizontalHeaderLabels(headLabels);
    ui->tableView_ReCallSource->setModel(m_recallsrctablemodel);
    ui->tableView_ReCallSource->setSelectionBehavior( QAbstractItemView::SelectRows );
    ui->tableView_ReCallSource->setEditTriggers(QAbstractItemView::NoEditTriggers);//20111026 yzy设置表格为只读
    ui->tableView_ReCallSource->setColumnWidth(0,300);
    ui->tableView_ReCallSource->setColumnWidth(1,500);

    QStringList headLabels1;
    headLabels1.append(tr("Fault Time"));
    headLabels1.append(tr("Fault Info"));
    m_recallrecordtablemodel=new QStandardItemModel(this);
    m_recallrecordtablemodel->setColumnCount(headLabels1.count());
    m_recallrecordtablemodel->setHorizontalHeaderLabels(headLabels1);
    ui->tableView_ReCallData->setModel(m_recallrecordtablemodel);
    ui->tableView_ReCallData->setSelectionBehavior( QAbstractItemView::SelectRows );
    ui->tableView_ReCallData->setEditTriggers(QAbstractItemView::NoEditTriggers);//20111026 yzy设置表格为只读
    ui->tableView_ReCallData->setColumnWidth(0,300);
    ui->tableView_ReCallData->setColumnWidth(1,500);

}

void HistoryDataDlg::setupChart(void)
{
    //ui->curve->setModel(model);
    ui->curve->setting().border().setRight(30);
    ui->curve->setting().border().setLeft(180);
    ui->curve->setting().border().setBackgroundColor(QColor(255,255,255));
    ui->curve->setting().grid().horizzontalTick().setTickMajor(13);
    ui->curve->setting().grid().horizzontalTick().setTickMinor(6);
    ui->curve->setting().grid().verticalTick().setTickMajor(7);
    ui->curve->setting().grid().verticalTick().setTickMinor(6);
    ui->curve->setting().grid().setBackgroundColor(QColor(192,192,192));
    ui->curve->setting().grid().setForegroundColor(QColor(0,0,0));
    ui->curve->setting().scale().setAutoCurrentLimit(true);
    ui->curve->setting().scale().setNominalAutoLimit(true);
    ui->curve->updateChart();
}

//表格单元格编辑
void HistoryDataDlg::dataChangedSlot(const QModelIndex & topLeft,
                                 const QModelIndex & bottomRight)
{
    //如果是时间列则不处理
    if((topLeft.column()%2))
        return;
    QVariant val;
    //当前值
    val=m_tablemodel->data(topLeft);
    //时间
}

//用户登录
int HistoryDataDlg::userLogin(quint32 usrid)
{
    if( !m_entryDlg )
    {
        m_entryDlg = new EntryDlg(this);
    }

    if(usrid<1)
    {
        if( m_entryDlg->showDlg("hisdman") != QDialog::Accepted )
        {
            return 0;
        }
    }
    else
    {
        if(!m_entryDlg->loadEntryInfoByUserId(usrid))
        {
            return 0;
        }
    }
    return 1;
}


void HistoryDataDlg::findYcPointSlot()
{
    QString pointdesc=ui->lineEdit_point->text().simplified();
    if(pointdesc.isEmpty())
        return;
    ui->listWidget_speedPoint->clear();
    ui->listWidget_YCPoint->clear();
    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;

    QVariant val;
    int staid=0;
    int meastypeid=0;
    val=ui->comboBox_YCStation->itemData(ui->comboBox_YCStation->currentIndex());
    staid=val.toInt();
    val=ui->comboBox_YCMeasType->itemData(ui->comboBox_YCMeasType->currentIndex());
    meastypeid=val.toInt();
    if(staid<1)//||meastypeid<1)
        return;

    int i=0;
    int pos=0;
    QListWidgetItem *item=NULL;
    QStringList ycPClass;
    ycPClass<<"2"<<"10"<<"5"<<"1"<<"13"<<"21"<<"20";
    int pclass=0;
    QString pdesc=QString::null;
    qDebug()<<"staid="<<staid<<"meastypeid="<<meastypeid;
    for(i=1;i<=SCADA_PointTypeCat->extent;i++)
//    for(i=1;i<=SCADA_PointDevCat->extent;i++)
    {
//        if(SCADA_PointDev[i].devid==0)
//           continue;
        if(SCADA_PointType[i].station_idx!=staid)
//           ||SCADA_PointDev[i].meastype!=meastypeid)//大于0小于600 遥测
        {
            continue;
        }

        pdesc=QString::fromLocal8Bit(SCADA_PointDesc[i].point_desc);
        pclass=SCADA_PointType[i].point_class;
        if(!pointdesc.isEmpty()&&!pdesc.contains(pointdesc) || !ycPClass.contains(QString::number(pclass)))
        {
            continue;
        }
        ui->listWidget_YCPoint->addItem( QString::fromLocal8Bit(SCADA_PointDesc[i].point_desc) );
        item=ui->listWidget_YCPoint->item(pos);
        item->setData(Item_UserRole_Pid,SCADA_PointType[i].pid);
        item->setData(Item_UserRole_SaveInterval,SCADA_PointSave[i].save_interval);
        pos++;
    }
}

void HistoryDataDlg::findYxPointSlot()
{
    ui->listWidget_YXPoint->clear();
    if(!IsHasMapScadaDatabase())
        MMI_MapScadaDatabase();
    if(!IsHasMapScadaDatabase())
        return;

    QVariant val;
    int staid=0;
    int meastypeid=0;
    val=ui->comboBox_YXStation->itemData(ui->comboBox_YXStation->currentIndex());
    staid=val.toInt();
    val=ui->comboBox_YXMeasType->itemData(ui->comboBox_YXMeasType->currentIndex());
    meastypeid=val.toInt();
    if(staid<1||meastypeid<1)
        return;

    int i=0;
    int pos=0;
    QListWidgetItem *item=NULL;
    QString pointdesc=ui->lineEdit_point_yx->text().simplified();
    QStringList yxPClass;
    yxPClass<<"7"<<"11"<<"6"<<"22";
    int pclass=0;
    QString pdesc=QString::null;
    for(i=1;i<=SCADA_PointTypeCat->extent;i++)
//    for(i=1;i<=SCADA_PointDevCat->extent;i++)
    {

        if(SCADA_PointType[i].station_idx!=staid
           ||SCADA_PointDev[i].meastype!=meastypeid)//大于0小于600 遥测
        {
            continue;
        }
//        if(SCADA_PointDev[i].devid==0)
//           continue;
        pdesc=SCADA_PointDesc[i].point_desc;
        pclass=SCADA_PointType[i].point_class;
        if(!pointdesc.isEmpty()&&!pdesc.contains(pointdesc) || !yxPClass.contains(QString::number(pclass)))
        {
            continue;
        }
        ui->listWidget_YXPoint->addItem( QString::fromLocal8Bit(SCADA_PointDesc[i].point_desc) );
        item=ui->listWidget_YXPoint->item(pos);
        item->setData(Item_UserRole_Pid,SCADA_PointType[i].pid);
        item->setData(Item_UserRole_SaveInterval,SCADA_PointSave[i].save_interval);
        pos++;
    }
}

/***********************
函数名称：InitialJXQueryControl
函数说明：初始化检修历史控件
函数参数：
    (in)
    (out)
返回值：void
创建人：wuqingxiong
创建时间：20221213
***********************/
void HistoryDataDlg::InitialJXQueryControl()
{
    m_checkLastJXTime = false;
    m_curJXDevId = 0;
    m_staid = 0;
    m_JXQueryConditions.clear();

    ui->lineEdit_JXStationQuery->setText("");
    ui->lineEdit_JXPointDesc->setText("");

    QDateTime dt = QDateTime::currentDateTime();
    QDateTime startDT = dt.addMonths(-1);
    ui->checkBox_lastJXTime->setCheckState(Qt::Unchecked);
    ui->dateTimeEdit_startLastJXTime->setDateTime(startDT);
    ui->dateTimeEdit_endLastJXTime->setDateTime(dt);


    ui->listWidget_JXStation->clear();
    QVariantList vallist=MMI_GetSCADAStationList();
    QListWidgetItem *item=NULL;
    QVariantHash hash;

    ui->listWidget_JXStation->blockSignals(true);
    for(int i=0;i<vallist.count();i++)
    {
        hash=vallist.at(i).toHash();
        ui->listWidget_JXStation->addItem(hash.value("station_desc").toString());
        item=ui->listWidget_JXStation->item(i);
        item->setData(Qt::UserRole,hash);
        item->setCheckState(Qt::Checked);
        item->setFlags(Qt::ItemIsUserCheckable | item->flags());
    }
    ui->listWidget_JXStation->blockSignals(false);

    QStringList namelist;
    QStringList desclist;
    //序号、厂站、点描述、总运行时间、总开机次数、最后检修时间、已运行时间、开机次数、到期时间、运行时间限、提醒标志
    namelist <<"no"
            <<"stadesc"
           <<"desc"
          <<"allruntimes"
         <<"allrunnums"
        <<"laststattime"
       <<"runtimes"
      <<"runnums"
    <<"userName";

    desclist<<tr("No")<<tr("StationDesc")<<tr("PointDesc")
              <<tr("JXAllRunTimes(hour)")<<tr("JXAllRunNums")<<tr("JXLastStatTime")
                <<tr("JXRunTimes(hour)")<<tr("JXRunNums")<<tr("UserName");

    m_JXtablemodel=new QStandardItemModel(this);
    m_JXtablemodel->setColumnCount(namelist.count());
    m_JXtablemodel->setHorizontalHeaderLabels(desclist);
    ui->tableView_JXtable->setModel(m_JXtablemodel);
    //ui->tableView_JXtable->verticalHeader()->hide();
    //ui->tableView_JXtable->horizontalHeader()->setStretchLastSection(true);
    ui->tableView_JXtable->setEditTriggers(QTableView::NoEditTriggers);
    ui->tableView_JXtable->setSelectionMode(QTableView::NoSelection);
    ui->tableView_JXtable->setColumnWidth(namelist.indexOf("no"), 80);
    ui->tableView_JXtable->setColumnWidth(namelist.indexOf("stadesc"),200);
    ui->tableView_JXtable->setColumnWidth(namelist.indexOf("desc"),200);
    ui->tableView_JXtable->setColumnWidth(namelist.indexOf("allruntimes"),140);
    ui->tableView_JXtable->setColumnWidth(namelist.indexOf("runtimes"),140);
    ui->tableView_JXtable->setColumnWidth(namelist.indexOf("laststattime"),200);
    ui->tableView_JXtable->setColumnWidth(namelist.indexOf("userName"),100);
}

/*函数名称：
 *函数说明：全选
 *函数参数： 无
 *返回值：void
 *创建人：wuqingxiong
 *创建时间：20220829
 */
void HistoryDataDlg::on_pushButton_allSelect_clicked()
{
    ui->listWidget_JXStation->blockSignals(true);
    QListWidgetItem *item=NULL;
    for(int i=0;i<ui->listWidget_JXStation->count();i++)
    {
        item=ui->listWidget_JXStation->item(i);
        item->setCheckState(Qt::Checked);
    }
    ui->listWidget_JXStation->blockSignals(false);
}

/*函数名称：
 *函数说明：全不选事件
 *函数参数： 无
 *返回值：void
 *创建人：wuqingxiong
 *创建时间：20220829
 */
void HistoryDataDlg::on_pushButton_noSelect_clicked()
{
    ui->listWidget_JXStation->blockSignals(true);
    QListWidgetItem *item=NULL;
    for(int i=0;i<ui->listWidget_JXStation->count();i++)
    {
        item=ui->listWidget_JXStation->item(i);
        item->setCheckState(Qt::Unchecked);
    }
    ui->listWidget_JXStation->blockSignals(false);
}

void HistoryDataDlg::on_lineEdit_JXStationQuery_textChanged(const QString &arg1)
{
    QString stationQueryKeys = arg1;
    if(stationQueryKeys.length() > 0)
    {
        ui->listWidget_JXStation->clear();
        QVariantList vallist=MMI_GetSCADAStationList();
        QListWidgetItem *item=NULL;
        QVariantHash hash;
        ui->listWidget_JXStation->blockSignals(true);
        for(int i=0, j=0;i<vallist.count();i++)
        {
            hash=vallist.at(i).toHash();
            if(hash.value("station_desc").toString().contains(stationQueryKeys))
            {
                ui->listWidget_JXStation->addItem(hash.value("station_desc").toString());
                item=ui->listWidget_JXStation->item(j);
                item->setData(Qt::UserRole,hash);
                item->setCheckState(Qt::Unchecked);
                item->setFlags(Qt::ItemIsUserCheckable | item->flags());
                j++;
            }
        }
        ui->listWidget_JXStation->blockSignals(false);
    }
    else
    {
        ui->listWidget_JXStation->clear();
        QVariantList vallist=MMI_GetSCADAStationList();
        QListWidgetItem *item=NULL;
        QVariantHash hash;
        int pos=-1;
        ui->listWidget_JXStation->blockSignals(true);
        for(int i=0, j=0;i<vallist.count();i++)
        {
            hash=vallist.at(i).toHash();
            ui->listWidget_JXStation->addItem(hash.value("station_desc").toString());
            item=ui->listWidget_JXStation->item(i);
            item->setData(Qt::UserRole,hash);
            item->setCheckState(Qt::Unchecked);
            item->setFlags(Qt::ItemIsUserCheckable | item->flags());
        }
        ui->listWidget_JXStation->blockSignals(false);
    }
}

/*函数名称：
 *函数说明：查询事件
 *函数参数： 无
 *返回值：void
 *创建人：wuqingxiong
 *创建时间：20220829
 */
void HistoryDataDlg::on_pushButton_jxquery_clicked()
{
    QVariantHash conditionhash;
    conditionhash.clear();

    //获取厂站
    QVariantHash staHash;
    staHash.clear();
    ui->listWidget_JXStation->blockSignals(true);
    QListWidgetItem *item=NULL;
    for(int i=0;i<ui->listWidget_JXStation->count();i++)
    {
        item=ui->listWidget_JXStation->item(i);
        if(item->checkState() == Qt::Checked)
        {
            QVariantHash hash;
            hash=item->data(Qt::UserRole).toHash();
            int staid=hash.value("station_no").toUInt();
            QString strStaid = QString("%1").arg(staid);
            staHash.insert(strStaid, hash);
        }
    }
    ui->listWidget_JXStation->blockSignals(false);
    if(staHash.count() <= 0)
    {
        QMessageBox::information( this, tr("Info"), tr("no station selected!"));
        return;
    }
    qDebug()<< "staHash.count() = "<< staHash.count();
    conditionhash.insert("staList", staHash);

    //获取点描述
    QString pointDesc = ui->lineEdit_JXPointDesc->text();
    conditionhash.insert("pointDesc", pointDesc);

    //判断最后检修时间大小
    m_checkLastJXTime = ui->checkBox_lastJXTime->checkState();
    if(ui->checkBox_lastJXTime->checkState() == Qt::Checked)
    {
        quint64 startDT = ui->dateTimeEdit_startLastJXTime->dateTime().toTime_t();
        quint64 endDT = ui->dateTimeEdit_endLastJXTime->dateTime().toTime_t();
        if(startDT > endDT)
        {
            QMessageBox::information( this, tr("Info"), tr("LastJXTime startTime should less than or equal endTime!"));
            return;
        }
        conditionhash.insert("startLastJXTime", ui->dateTimeEdit_startLastJXTime->dateTime());
        conditionhash.insert("endLastJXTime", ui->dateTimeEdit_endLastJXTime->dateTime());
    }

    m_JXQueryConditions = conditionhash;

    FreshJXQueryTable();
}
