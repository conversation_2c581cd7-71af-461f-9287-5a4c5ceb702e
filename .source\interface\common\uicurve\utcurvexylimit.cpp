#include "utcurvexylimit.h"

UTCurveXYLimit::UTCurveXYLimit()
{
}

UTCurveXYLimit::UTCurveXYLimit(const UTCurveXYLimit &limit)
{
    m_limit_x=limit.m_limit_x;
    m_limit_y=limit.m_limit_y;
}

UTCurveXYLimit::UTCurveXYLimit(const UTCurveXYLimitAxis &limit_x,const UTCurveXYLimitAxis &limit_y)
{
    m_limit_x=limit_x;
    m_limit_y=limit_y;
}


const UTCurveXYLimitAxis &UTCurveXYLimit::limitX(void) const
{
    return(m_limit_x);
}

const UTCurveXYLimitAxis &UTCurveXYLimit::limitY(void) const
{
    return(m_limit_y);
}

QVariant::Type UTCurveXYLimit::type(void) const
{
    if(m_limit_x.type()!=m_limit_y.type()) return(QVariant::Invalid);

    return(m_limit_x.type());
}

bool UTCurveXYLimit::canConvert(QVariant::Type type) const
{
    if(
            (m_limit_x.canConvert(type))&&
            (m_limit_y.canConvert(type))
            )
    {
        return(true);
    }
    return(false);
}


UTCurveXYLimit &UTCurveXYLimit::operator=(const UTCurveXYLimit &limit)
{
    if(&limit!=this)
    {
        m_limit_x=limit.m_limit_x;
        m_limit_y=limit.m_limit_y;
    }
    return(*this);
}

bool UTCurveXYLimit::operator==(const UTCurveXYLimit &limit) const
{
    if(m_limit_x!=limit.m_limit_x) return(false);
    if(m_limit_y!=limit.m_limit_y) return(false);
    return(true);
}

bool UTCurveXYLimit::operator!=(const UTCurveXYLimit &limit) const
{
    if(m_limit_x!=limit.m_limit_x) return(true);
    if(m_limit_y!=limit.m_limit_y) return(true);
    return(false);
}
