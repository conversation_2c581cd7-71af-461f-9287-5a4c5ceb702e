<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.0" language="en_US">
<context>
    <name>HisDataCopyDlg</name>
    <message>
        <location filename="hisdatacopydlg.ui" line="14"/>
        <source>HisDataCopyDlg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.ui" line="40"/>
        <source>SourceData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.ui" line="46"/>
        <location filename="hisdatacopydlg.ui" line="96"/>
        <source>StartTime:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.ui" line="63"/>
        <location filename="hisdatacopydlg.ui" line="113"/>
        <source>EndTime:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.ui" line="83"/>
        <source>StartCopy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.ui" line="90"/>
        <source>TargetData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.ui" line="133"/>
        <source>StopCopy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.ui" line="140"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="155"/>
        <source>Editing data, please wait...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="98"/>
        <location filename="hisdatacopydlg.cpp" line="105"/>
        <location filename="hisdatacopydlg.cpp" line="129"/>
        <location filename="hisdatacopydlg.cpp" line="136"/>
        <location filename="hisdatacopydlg.cpp" line="195"/>
        <source>Hint</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="99"/>
        <source>Source Start time big Target Start time!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="106"/>
        <source>Source End time big Target End time!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="130"/>
        <source>Souce start date must same to end date!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="137"/>
        <source>Target start date must same to end date!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="144"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="145"/>
        <source>Are you sure start coyp data?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="161"/>
        <source>Copy data stop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="179"/>
        <source>PointPara is NULL!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="186"/>
        <source>Copy data stop!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="194"/>
        <location filename="hisdatacopydlg.cpp" line="196"/>
        <source>Copy data finish!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="228"/>
        <source>Get %1 Data,please wait...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="242"/>
        <source>Current date can not get data!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="277"/>
        <source>Copy %1 Data,please wait...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdatacopydlg.cpp" line="313"/>
        <source>Finish copy %1 Data,please wait...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>HisDataEditDlg</name>
    <message>
        <location filename="hisdataeditdlg.ui" line="14"/>
        <source>HisDataEditDlg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="65"/>
        <source>TimeType</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="71"/>
        <source>Day</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="78"/>
        <source>Month</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="94"/>
        <source>DataType</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="100"/>
        <source>His Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="107"/>
        <source>Real Max</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="114"/>
        <source>Real Min</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="121"/>
        <source>Real Average</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="128"/>
        <source>Real HGL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="135"/>
        <source>Real Pqrate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="158"/>
        <source>Load</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="165"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.ui" line="172"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.cpp" line="204"/>
        <source>%1Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.cpp" line="220"/>
        <source>%1:00Hour</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="hisdataeditdlg.cpp" line="224"/>
        <source>%1Day</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>HisTrendCurveDlg</name>
    <message>
        <location filename="histrendcurvedlg.ui" line="14"/>
        <source>HisTrendCurveDlg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="32"/>
        <source>Station:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="39"/>
        <source>PointDesc:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="75"/>
        <source>TimeType</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="82"/>
        <source>From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="95"/>
        <source>To:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="110"/>
        <source>Query</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="117"/>
        <location filename="histrendcurvedlg.cpp" line="363"/>
        <source>PrevHour</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="124"/>
        <location filename="histrendcurvedlg.cpp" line="364"/>
        <source>NextHour</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="131"/>
        <source>NormalScale</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="151"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="164"/>
        <source>Curve</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.ui" line="188"/>
        <source>Table</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="52"/>
        <source>One Hour</source>
        <oldsource>Hour</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="53"/>
        <source>30 Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="54"/>
        <source>10 Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="55"/>
        <source>5 Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="69"/>
        <source>Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="69"/>
        <source>ChangeTime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="168"/>
        <source>His Trend curve</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="369"/>
        <source>Prev30Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="370"/>
        <source>Next30Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="375"/>
        <source>Prev10Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="376"/>
        <source>Next10Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="381"/>
        <source>Prev5Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="histrendcurvedlg.cpp" line="382"/>
        <source>Next5Minute</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>HistoryDataDlg</name>
    <message>
        <location filename="historydatadlg.ui" line="14"/>
        <source>HistoryDataDlg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="25"/>
        <location filename="recallCallback.cpp" line="26"/>
        <source>YC</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="328"/>
        <location filename="historydatadlg.ui" line="465"/>
        <location filename="historydatadlg.ui" line="915"/>
        <location filename="historydatadlg.ui" line="1129"/>
        <source>PointList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="387"/>
        <location filename="historydatadlg.ui" line="516"/>
        <location filename="historydatadlg.ui" line="669"/>
        <location filename="historydatadlg.ui" line="795"/>
        <location filename="historydatadlg.ui" line="921"/>
        <location filename="historydatadlg.ui" line="1135"/>
        <source>Area:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="373"/>
        <location filename="historydatadlg.ui" line="533"/>
        <location filename="historydatadlg.ui" line="686"/>
        <location filename="historydatadlg.ui" line="812"/>
        <location filename="historydatadlg.ui" line="938"/>
        <location filename="historydatadlg.ui" line="1152"/>
        <location filename="historydatadlg.ui" line="1286"/>
        <source>Station:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="380"/>
        <location filename="historydatadlg.ui" line="553"/>
        <location filename="historydatadlg.ui" line="965"/>
        <location filename="historydatadlg.ui" line="1169"/>
        <source>MeasType:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="416"/>
        <location filename="historydatadlg.ui" line="483"/>
        <source>PointName:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="35"/>
        <location filename="historydatadlg.ui" line="1221"/>
        <source>Curve</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="41"/>
        <source>ThirdCXCurve</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="59"/>
        <location filename="historydatadlg.ui" line="1258"/>
        <source>Table</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="72"/>
        <source>QuerySet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="78"/>
        <source>TimeType:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="95"/>
        <location filename="historydatadlg.ui" line="1355"/>
        <source>DataType:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="105"/>
        <location filename="historydatadlg.ui" line="115"/>
        <location filename="historydatadlg.ui" line="570"/>
        <location filename="historydatadlg.ui" line="703"/>
        <location filename="historydatadlg.ui" line="829"/>
        <location filename="historydatadlg.ui" line="998"/>
        <location filename="historydatadlg.ui" line="1332"/>
        <source>Time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="125"/>
        <location filename="historydatadlg.ui" line="1008"/>
        <location filename="historydatadlg.ui" line="1535"/>
        <source>To</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="138"/>
        <source>CurveList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="151"/>
        <location filename="historydatadlg.ui" line="1193"/>
        <location filename="historydatadlg.ui" line="1399"/>
        <source>Add</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="158"/>
        <location filename="historydatadlg.ui" line="1056"/>
        <location filename="historydatadlg.ui" line="1200"/>
        <location filename="historydatadlg.ui" line="1433"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="165"/>
        <location filename="historydatadlg.ui" line="1018"/>
        <location filename="historydatadlg.ui" line="1207"/>
        <source>Load</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="187"/>
        <source>PreDay</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="194"/>
        <source>NextDay</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="201"/>
        <source>YSet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="311"/>
        <source>EditData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="318"/>
        <source>CopyData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="290"/>
        <location filename="historydatadlg.ui" line="620"/>
        <location filename="historydatadlg.ui" line="746"/>
        <location filename="historydatadlg.ui" line="872"/>
        <source>StatisData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="304"/>
        <source>ExportTable</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="436"/>
        <location filename="historydatadlg.ui" line="503"/>
        <source>findPoint</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1123"/>
        <source>speed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="297"/>
        <source>Print</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="221"/>
        <source>RealDayStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="228"/>
        <source>RealMonStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="235"/>
        <source>RealYearStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="242"/>
        <location filename="historydatadlg.ui" line="599"/>
        <source>HisDayStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="249"/>
        <location filename="historydatadlg.ui" line="606"/>
        <location filename="historydatadlg.ui" line="732"/>
        <location filename="historydatadlg.ui" line="858"/>
        <source>HisMonStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="256"/>
        <location filename="historydatadlg.ui" line="613"/>
        <location filename="historydatadlg.ui" line="739"/>
        <location filename="historydatadlg.ui" line="865"/>
        <source>HisYearStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="263"/>
        <source>RealTrend</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="283"/>
        <location filename="historydatadlg.ui" line="640"/>
        <location filename="historydatadlg.ui" line="766"/>
        <location filename="historydatadlg.ui" line="892"/>
        <location filename="historydatadlg.ui" line="1112"/>
        <location filename="historydatadlg.ui" line="1227"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="453"/>
        <location filename="recallCallback.cpp" line="27"/>
        <source>YX</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="651"/>
        <source>Rtu</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="663"/>
        <source>RtuList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="777"/>
        <source>Channel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="789"/>
        <source>ChannelList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="903"/>
        <source>ReCall</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="955"/>
        <source>YXYCType:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="992"/>
        <source>TimeSet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1069"/>
        <source>LoadData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1081"/>
        <source>DataInfo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1272"/>
        <source>ManualMonthData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1309"/>
        <source>PointDesc:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1372"/>
        <location filename="historydatadlg.ui" line="1558"/>
        <source>Query</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1406"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1426"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1440"/>
        <source>Import</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1466"/>
        <source>RepairHisInfo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1472"/>
        <source>station</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1501"/>
        <location filename="historydatadlg.cpp" line="1344"/>
        <source>PointDesc</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1525"/>
        <location filename="historydatadlg.ui" line="1542"/>
        <source>yyyy-MM-dd HH:mm:ss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1518"/>
        <source>lastJXTime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1596"/>
        <source>selectAll</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1603"/>
        <source>NoSelect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="77"/>
        <source>His Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="78"/>
        <source>Real Max Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="78"/>
        <source>Real Min Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="79"/>
        <source>Real Average Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="79"/>
        <source>Real HGL Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="80"/>
        <source>Real Pqrate Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="81"/>
        <source>Stat Max Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="81"/>
        <source>Stat Min Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="82"/>
        <source>Stat Average Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="82"/>
        <source>Stat HGL Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="83"/>
        <source>Stat Pqrate Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="84"/>
        <source>Kwh Meter Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="84"/>
        <source>Kwh Sum By Interval</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="104"/>
        <source>P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="104"/>
        <source>Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="105"/>
        <source>Voltage</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="105"/>
        <source>Current</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="106"/>
        <source>Tap</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="106"/>
        <source>Temperature</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="107"/>
        <source>Cos</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="107"/>
        <source>Hz</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="108"/>
        <source>P Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="108"/>
        <source>Q Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="109"/>
        <source>Forward P Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="109"/>
        <source>Rev P Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="110"/>
        <source>Forward Q Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="110"/>
        <source>Rev Q Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="111"/>
        <source>A P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="111"/>
        <source>B P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="112"/>
        <source>C P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="112"/>
        <source>0 P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="113"/>
        <source>A Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="113"/>
        <source>B Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="114"/>
        <source>C Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="114"/>
        <source>0 Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="115"/>
        <source>A Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="115"/>
        <source>B Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="116"/>
        <source>C Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="116"/>
        <source>0 Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="117"/>
        <source>AB Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="117"/>
        <source>BC Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="118"/>
        <source>CA Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="118"/>
        <source>A I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="119"/>
        <source>B I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="119"/>
        <source>C I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="120"/>
        <source>0 I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="121"/>
        <source>PLOSS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="122"/>
        <source>EFFIDX</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="123"/>
        <source>BEARLEVEL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="124"/>
        <source>LOADLEVEL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="125"/>
        <source>VOLUB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="126"/>
        <source>CRTUB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="127"/>
        <source>UNITCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="128"/>
        <source>MDQUOTA</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="129"/>
        <source>TOTCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="130"/>
        <source>HIGHCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="131"/>
        <source>MEDCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="132"/>
        <source>LOWCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="133"/>
        <source>TOTCOST</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="134"/>
        <source>HIGHCOST</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="135"/>
        <source>MEDCOST</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="136"/>
        <source>LOWCOST</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="171"/>
        <source>Breaker State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="171"/>
        <source>Switch State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="172"/>
        <source>Protect Signal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="172"/>
        <source>SGZ Signal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="173"/>
        <source>Normal State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="173"/>
        <source>A Breaker State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="174"/>
        <source>B Breaker State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="174"/>
        <source>C Breaker State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="175"/>
        <source>State1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="175"/>
        <source>State32</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="196"/>
        <source>Normal change times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="197"/>
        <source>Fault change times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="198"/>
        <source>YK times(YT times)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="199"/>
        <source>Change to open times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="200"/>
        <source>YK refuse change times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="201"/>
        <source>change to close times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="202"/>
        <source>change to 00 times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="203"/>
        <source>change to 11 times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="204"/>
        <source>close time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="205"/>
        <source>00 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="206"/>
        <source>11 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="224"/>
        <source>Rtu run time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="225"/>
        <source>Rtu falut time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="226"/>
        <source>Rtu stop times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="236"/>
        <source>Channel run time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="237"/>
        <source>Channel stop times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="241"/>
        <source>Statistic Item</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="242"/>
        <source>Statistic Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="737"/>
        <source>TimeToTime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1100"/>
        <location filename="historydatadlg.cpp" line="1112"/>
        <source>Fault Time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1101"/>
        <location filename="historydatadlg.cpp" line="1113"/>
        <source>Fault Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1344"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1344"/>
        <source>StationDesc</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1345"/>
        <source>JXAllRunTimes(hour)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1345"/>
        <source>JXAllRunNums</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1345"/>
        <source>JXLastStatTime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1346"/>
        <source>JXRunTimes(hour)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1346"/>
        <source>JXRunNums</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1346"/>
        <source>UserName</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1481"/>
        <source>no station selected!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.cpp" line="1499"/>
        <source>LastJXTime startTime should less than or equal endTime!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="37"/>
        <source>his data month average</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="40"/>
        <source>his data month max time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="41"/>
        <source>his data month min time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="42"/>
        <source>his data stat qualified rate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="43"/>
        <source>his data real qualified time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="44"/>
        <source>his data real hover time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="45"/>
        <source>his data real lover time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="46"/>
        <source>his data stat hover rate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="47"/>
        <source>his data stat lover rate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="48"/>
        <source>his data stat all time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="82"/>
        <source>all data type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="106"/>
        <source>check all</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="107"/>
        <source>point desc</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="108"/>
        <source>stat month time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="237"/>
        <source>Input Err</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="237"/>
        <source>input invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="317"/>
        <location filename="manStatCallback.cpp" line="324"/>
        <location filename="manStatCallback.cpp" line="832"/>
        <location filename="manStatCallback.cpp" line="835"/>
        <location filename="manStatCallback.cpp" line="1011"/>
        <location filename="manStatCallback.cpp" line="1014"/>
        <source>info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="317"/>
        <source>Save successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="324"/>
        <source>None select or None to be changed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="423"/>
        <source>Cancel Err</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="423"/>
        <source>Cancel data change failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="453"/>
        <source>Please select time first</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="642"/>
        <source>Please select point first</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="647"/>
        <source>Please select stat time first</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="657"/>
        <source>point is not exist!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="711"/>
        <location filename="manStatCallback.cpp" line="721"/>
        <source>current point stat time data exist!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="735"/>
        <source>add failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="832"/>
        <source>None selected need to delete !</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="835"/>
        <source>Do you want to delete current selected %1 Points</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="908"/>
        <source>all</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="935"/>
        <source>Open CsvFile</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="935"/>
        <source>Files (*.csv)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="947"/>
        <source>This operation will save the db directly.Sure to continue?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="1011"/>
        <source>import csvfile complete!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="1014"/>
        <source>import result: failed(%1),skip(%2)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="38"/>
        <source>his data month max</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="manStatCallback.cpp" line="39"/>
        <source>his data month min</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="126"/>
        <location filename="buttonCallback.cpp" line="149"/>
        <location filename="buttonCallback.cpp" line="216"/>
        <location filename="buttonCallback.cpp" line="229"/>
        <location filename="buttonCallback.cpp" line="599"/>
        <location filename="buttonCallback.cpp" line="642"/>
        <location filename="buttonCallback.cpp" line="660"/>
        <location filename="buttonCallback.cpp" line="670"/>
        <location filename="buttonCallback.cpp" line="732"/>
        <location filename="buttonCallback.cpp" line="774"/>
        <location filename="buttonCallback.cpp" line="817"/>
        <location filename="buttonCallback.cpp" line="824"/>
        <location filename="buttonCallback.cpp" line="870"/>
        <location filename="buttonCallback.cpp" line="877"/>
        <location filename="buttonCallback.cpp" line="901"/>
        <location filename="buttonCallback.cpp" line="921"/>
        <location filename="buttonCallback.cpp" line="941"/>
        <location filename="buttonCallback.cpp" line="1145"/>
        <location filename="buttonCallback.cpp" line="1152"/>
        <location filename="buttonCallback.cpp" line="1174"/>
        <location filename="buttonCallback.cpp" line="1180"/>
        <location filename="historydatadlg.cpp" line="1481"/>
        <location filename="historydatadlg.cpp" line="1499"/>
        <location filename="manStatCallback.cpp" line="453"/>
        <location filename="manStatCallback.cpp" line="642"/>
        <location filename="manStatCallback.cpp" line="647"/>
        <location filename="manStatCallback.cpp" line="657"/>
        <location filename="manStatCallback.cpp" line="711"/>
        <location filename="manStatCallback.cpp" line="721"/>
        <location filename="manStatCallback.cpp" line="735"/>
        <location filename="manStatCallback.cpp" line="946"/>
        <location filename="recallCallback.cpp" line="206"/>
        <location filename="statisCallback.cpp" line="106"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="126"/>
        <source>Please select one yc point first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="149"/>
        <location filename="buttonCallback.cpp" line="660"/>
        <source>The time set max is %1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="217"/>
        <source>This point saveinfo is not set ok,please check!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="230"/>
        <location filename="buttonCallback.cpp" line="671"/>
        <source>Point query time must is same</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="312"/>
        <location filename="historydatadlg.cpp" line="731"/>
        <location filename="statisCallback.cpp" line="58"/>
        <source>Day</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="316"/>
        <location filename="historydatadlg.cpp" line="733"/>
        <location filename="statisCallback.cpp" line="66"/>
        <source>Month</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="320"/>
        <location filename="historydatadlg.cpp" line="735"/>
        <location filename="statisCallback.cpp" line="73"/>
        <source>Year</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="599"/>
        <location filename="buttonCallback.cpp" line="642"/>
        <location filename="buttonCallback.cpp" line="732"/>
        <location filename="buttonCallback.cpp" line="774"/>
        <location filename="buttonCallback.cpp" line="817"/>
        <location filename="buttonCallback.cpp" line="824"/>
        <location filename="buttonCallback.cpp" line="870"/>
        <location filename="buttonCallback.cpp" line="877"/>
        <location filename="buttonCallback.cpp" line="1145"/>
        <location filename="buttonCallback.cpp" line="1152"/>
        <location filename="buttonCallback.cpp" line="1174"/>
        <location filename="buttonCallback.cpp" line="1180"/>
        <source>Please select one curve first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="901"/>
        <location filename="buttonCallback.cpp" line="921"/>
        <location filename="buttonCallback.cpp" line="941"/>
        <source>Please select one YX point first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="962"/>
        <source>Are you sure to statistic%1day datas?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="965"/>
        <location filename="buttonCallback.cpp" line="975"/>
        <location filename="buttonCallback.cpp" line="978"/>
        <location filename="buttonCallback.cpp" line="1016"/>
        <location filename="buttonCallback.cpp" line="1023"/>
        <location filename="buttonCallback.cpp" line="1078"/>
        <location filename="recallCallback.cpp" line="313"/>
        <source>Hint</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="976"/>
        <source>Run Statistic Successed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="979"/>
        <source>Run Statistic Failed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="historydatadlg.ui" line="1416"/>
        <location filename="buttonCallback.cpp" line="1000"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="1017"/>
        <source>Remove old %1 file failed,can not export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="1024"/>
        <source>Open %1 file failed,can not export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="1046"/>
        <source>Time(Hour)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="1049"/>
        <source>Time(Day)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="1052"/>
        <source>Time(Month)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="1055"/>
        <source>Time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="1061"/>
        <source>PointValie</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="buttonCallback.cpp" line="1079"/>
        <source>Export table to %1 ok</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="recallCallback.cpp" line="206"/>
        <source>fetch Fault data failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="recallCallback.cpp" line="222"/>
        <source>%1Year%2Mon%3Day%4Hour%5Min%6Sec%7Mse</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="recallCallback.cpp" line="251"/>
        <location filename="recallCallback.cpp" line="256"/>
        <source>SelectFault</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="recallCallback.cpp" line="252"/>
        <source>Please select fault source!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="recallCallback.cpp" line="257"/>
        <source>Are you sure to delete fault source?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="recallCallback.cpp" line="314"/>
        <source>No Data.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="statisCallback.cpp" line="78"/>
        <source>Statistic Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="statisCallback.cpp" line="106"/>
        <source>Have no data from database!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>JKHistoryDataDlg</name>
    <message>
        <location filename="jkhistorydatadlg.ui" line="14"/>
        <source>HistoryDataDlg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="40"/>
        <location filename="jkrecallCallback.cpp" line="26"/>
        <source>YC</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="66"/>
        <location filename="jkhistorydatadlg.ui" line="669"/>
        <location filename="jkhistorydatadlg.ui" line="1159"/>
        <location filename="jkhistorydatadlg.ui" line="1452"/>
        <source>PointList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="69"/>
        <location filename="jkhistorydatadlg.ui" line="172"/>
        <location filename="jkhistorydatadlg.ui" line="203"/>
        <location filename="jkhistorydatadlg.ui" line="318"/>
        <location filename="jkhistorydatadlg.ui" line="373"/>
        <location filename="jkhistorydatadlg.ui" line="672"/>
        <location filename="jkhistorydatadlg.ui" line="785"/>
        <location filename="jkhistorydatadlg.ui" line="813"/>
        <location filename="jkhistorydatadlg.ui" line="1162"/>
        <location filename="jkhistorydatadlg.ui" line="1277"/>
        <location filename="jkhistorydatadlg.ui" line="1328"/>
        <location filename="jkhistorydatadlg.ui" line="1413"/>
        <location filename="jkhistorydatadlg.ui" line="1455"/>
        <location filename="jkhistorydatadlg.ui" line="1776"/>
        <location filename="jkhistorydatadlg.ui" line="1902"/>
        <location filename="jkhistorydatadlg.ui" line="1973"/>
        <location filename="jkhistorydatadlg.ui" line="2052"/>
        <source>true</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="81"/>
        <location filename="jkhistorydatadlg.ui" line="753"/>
        <location filename="jkhistorydatadlg.ui" line="899"/>
        <location filename="jkhistorydatadlg.ui" line="1025"/>
        <location filename="jkhistorydatadlg.ui" line="1215"/>
        <location filename="jkhistorydatadlg.ui" line="1481"/>
        <source>Area:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="114"/>
        <location filename="jkhistorydatadlg.ui" line="760"/>
        <location filename="jkhistorydatadlg.ui" line="916"/>
        <location filename="jkhistorydatadlg.ui" line="1042"/>
        <location filename="jkhistorydatadlg.ui" line="1208"/>
        <location filename="jkhistorydatadlg.ui" line="1474"/>
        <location filename="jkhistorydatadlg.ui" line="1688"/>
        <source>Station:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="131"/>
        <location filename="jkhistorydatadlg.ui" line="767"/>
        <location filename="jkhistorydatadlg.ui" line="1201"/>
        <location filename="jkhistorydatadlg.ui" line="1467"/>
        <source>MeasType:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="154"/>
        <location filename="jkhistorydatadlg.ui" line="746"/>
        <source>PointName:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="169"/>
        <location filename="jkhistorydatadlg.ui" line="782"/>
        <source>findPoint</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="193"/>
        <location filename="jkhistorydatadlg.ui" line="300"/>
        <location filename="jkhistorydatadlg.ui" line="1403"/>
        <source>hline</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="200"/>
        <source>QuerySet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="225"/>
        <source>TimeType:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="242"/>
        <location filename="jkhistorydatadlg.ui" line="269"/>
        <location filename="jkhistorydatadlg.ui" line="713"/>
        <location filename="jkhistorydatadlg.ui" line="933"/>
        <location filename="jkhistorydatadlg.ui" line="1059"/>
        <location filename="jkhistorydatadlg.ui" line="1289"/>
        <location filename="jkhistorydatadlg.ui" line="1734"/>
        <source>Time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="252"/>
        <location filename="jkhistorydatadlg.ui" line="1299"/>
        <location filename="jkhistorydatadlg.ui" line="2024"/>
        <source>To</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="262"/>
        <location filename="jkhistorydatadlg.ui" line="1761"/>
        <source>DataType:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="315"/>
        <source>CurveList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="325"/>
        <location filename="jkhistorydatadlg.ui" line="1541"/>
        <location filename="jkhistorydatadlg.ui" line="1806"/>
        <source>Add</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="332"/>
        <location filename="jkhistorydatadlg.ui" line="1378"/>
        <location filename="jkhistorydatadlg.ui" line="1548"/>
        <location filename="jkhistorydatadlg.ui" line="1840"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="339"/>
        <location filename="jkhistorydatadlg.ui" line="1325"/>
        <location filename="jkhistorydatadlg.ui" line="1555"/>
        <source>Load</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="370"/>
        <location filename="jkhistorydatadlg.ui" line="810"/>
        <location filename="jkhistorydatadlg.ui" line="1970"/>
        <location filename="jkhistorydatadlg.cpp" line="119"/>
        <source>His Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="396"/>
        <source>ThirdCXCurve</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="419"/>
        <location filename="jkhistorydatadlg.ui" line="1590"/>
        <source>Curve</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="432"/>
        <location filename="jkhistorydatadlg.ui" line="1622"/>
        <source>Table</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="449"/>
        <source>PreDay</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="456"/>
        <source>NextDay</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="463"/>
        <location filename="jkhistorydatadlg.ui" line="846"/>
        <location filename="jkhistorydatadlg.ui" line="976"/>
        <location filename="jkhistorydatadlg.ui" line="1102"/>
        <source>StatisData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="470"/>
        <source>Print</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="477"/>
        <source>ExportTable</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="484"/>
        <source>EditData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="491"/>
        <source>CopyData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="518"/>
        <source>RealDayStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="525"/>
        <source>RealMonStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="532"/>
        <source>RealYearStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="539"/>
        <location filename="jkhistorydatadlg.ui" line="825"/>
        <source>HisDayStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="546"/>
        <location filename="jkhistorydatadlg.ui" line="832"/>
        <location filename="jkhistorydatadlg.ui" line="962"/>
        <location filename="jkhistorydatadlg.ui" line="1088"/>
        <source>HisMonStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="553"/>
        <location filename="jkhistorydatadlg.ui" line="839"/>
        <location filename="jkhistorydatadlg.ui" line="969"/>
        <location filename="jkhistorydatadlg.ui" line="1095"/>
        <source>HisYearStatis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="560"/>
        <source>RealTrend</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="643"/>
        <location filename="jkrecallCallback.cpp" line="27"/>
        <source>YX</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="866"/>
        <location filename="jkhistorydatadlg.ui" line="996"/>
        <location filename="jkhistorydatadlg.ui" line="1122"/>
        <location filename="jkhistorydatadlg.ui" line="1348"/>
        <location filename="jkhistorydatadlg.ui" line="2098"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="881"/>
        <source>Rtu</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="893"/>
        <source>RtuList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1007"/>
        <source>Channel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1019"/>
        <source>ChannelList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1133"/>
        <source>ReCall</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1194"/>
        <source>YXYCType:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1274"/>
        <source>TimeSet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1371"/>
        <source>LoadData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1410"/>
        <source>DataInfo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1426"/>
        <source>speed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1651"/>
        <source>ManualMonthData</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1711"/>
        <source>PointDesc:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1741"/>
        <source>yyyy-MM-dd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1773"/>
        <location filename="jkhistorydatadlg.ui" line="2049"/>
        <source>Query</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1813"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1823"/>
        <location filename="jkbuttonCallback.cpp" line="1000"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1833"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1847"/>
        <source>Import</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1873"/>
        <source>RepairHisInfo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1899"/>
        <source>Station List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1914"/>
        <source>station</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1931"/>
        <source>selectAll</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1938"/>
        <source>NoSelect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="1990"/>
        <location filename="jkhistorydatadlg.cpp" line="1532"/>
        <source>PointDesc</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="2007"/>
        <source>lastJXTime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.ui" line="2014"/>
        <location filename="jkhistorydatadlg.ui" line="2031"/>
        <source>yyyy-MM-dd HH:mm:ss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="126"/>
        <location filename="jkbuttonCallback.cpp" line="149"/>
        <location filename="jkbuttonCallback.cpp" line="216"/>
        <location filename="jkbuttonCallback.cpp" line="229"/>
        <location filename="jkbuttonCallback.cpp" line="599"/>
        <location filename="jkbuttonCallback.cpp" line="642"/>
        <location filename="jkbuttonCallback.cpp" line="660"/>
        <location filename="jkbuttonCallback.cpp" line="670"/>
        <location filename="jkbuttonCallback.cpp" line="732"/>
        <location filename="jkbuttonCallback.cpp" line="774"/>
        <location filename="jkbuttonCallback.cpp" line="817"/>
        <location filename="jkbuttonCallback.cpp" line="824"/>
        <location filename="jkbuttonCallback.cpp" line="870"/>
        <location filename="jkbuttonCallback.cpp" line="877"/>
        <location filename="jkbuttonCallback.cpp" line="901"/>
        <location filename="jkbuttonCallback.cpp" line="921"/>
        <location filename="jkbuttonCallback.cpp" line="941"/>
        <location filename="jkbuttonCallback.cpp" line="1145"/>
        <location filename="jkbuttonCallback.cpp" line="1152"/>
        <location filename="jkbuttonCallback.cpp" line="1174"/>
        <location filename="jkbuttonCallback.cpp" line="1180"/>
        <location filename="jkhistorydatadlg.cpp" line="1669"/>
        <location filename="jkhistorydatadlg.cpp" line="1687"/>
        <location filename="jkmanStatCallback.cpp" line="453"/>
        <location filename="jkmanStatCallback.cpp" line="642"/>
        <location filename="jkmanStatCallback.cpp" line="647"/>
        <location filename="jkmanStatCallback.cpp" line="657"/>
        <location filename="jkmanStatCallback.cpp" line="711"/>
        <location filename="jkmanStatCallback.cpp" line="721"/>
        <location filename="jkmanStatCallback.cpp" line="735"/>
        <location filename="jkmanStatCallback.cpp" line="946"/>
        <location filename="jkrecallCallback.cpp" line="206"/>
        <location filename="jkstatisCallback.cpp" line="106"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="126"/>
        <source>Please select one yc point first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="149"/>
        <location filename="jkbuttonCallback.cpp" line="660"/>
        <source>The time set max is %1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="217"/>
        <source>This point saveinfo is not set ok,please check!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="230"/>
        <location filename="jkbuttonCallback.cpp" line="671"/>
        <source>Point query time must is same</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="312"/>
        <location filename="jkhistorydatadlg.cpp" line="919"/>
        <location filename="jkstatisCallback.cpp" line="58"/>
        <source>Day</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="316"/>
        <location filename="jkhistorydatadlg.cpp" line="921"/>
        <location filename="jkstatisCallback.cpp" line="66"/>
        <source>Month</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="320"/>
        <location filename="jkhistorydatadlg.cpp" line="923"/>
        <location filename="jkstatisCallback.cpp" line="73"/>
        <source>Year</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="599"/>
        <location filename="jkbuttonCallback.cpp" line="642"/>
        <location filename="jkbuttonCallback.cpp" line="732"/>
        <location filename="jkbuttonCallback.cpp" line="774"/>
        <location filename="jkbuttonCallback.cpp" line="817"/>
        <location filename="jkbuttonCallback.cpp" line="824"/>
        <location filename="jkbuttonCallback.cpp" line="870"/>
        <location filename="jkbuttonCallback.cpp" line="877"/>
        <location filename="jkbuttonCallback.cpp" line="1145"/>
        <location filename="jkbuttonCallback.cpp" line="1152"/>
        <location filename="jkbuttonCallback.cpp" line="1174"/>
        <location filename="jkbuttonCallback.cpp" line="1180"/>
        <source>Please select one curve first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="901"/>
        <location filename="jkbuttonCallback.cpp" line="921"/>
        <location filename="jkbuttonCallback.cpp" line="941"/>
        <source>Please select one YX point first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="962"/>
        <source>Are you sure to statistic%1day datas?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="965"/>
        <location filename="jkbuttonCallback.cpp" line="975"/>
        <location filename="jkbuttonCallback.cpp" line="978"/>
        <location filename="jkbuttonCallback.cpp" line="1016"/>
        <location filename="jkbuttonCallback.cpp" line="1023"/>
        <location filename="jkbuttonCallback.cpp" line="1078"/>
        <location filename="jkrecallCallback.cpp" line="313"/>
        <location filename="jkrecallCallback.cpp" line="419"/>
        <source>Hint</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="976"/>
        <source>Run Statistic Successed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="979"/>
        <source>Run Statistic Failed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="1017"/>
        <source>Remove old %1 file failed,can not export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="1024"/>
        <source>Open %1 file failed,can not export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="1046"/>
        <source>Time(Hour)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="1049"/>
        <source>Time(Day)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="1052"/>
        <source>Time(Month)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="1055"/>
        <source>Time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="1061"/>
        <source>PointValie</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkbuttonCallback.cpp" line="1079"/>
        <source>Export table to %1 ok</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="120"/>
        <source>Real Max Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="120"/>
        <source>Real Min Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="121"/>
        <source>Real Average Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="121"/>
        <source>Real HGL Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="122"/>
        <source>Real Pqrate Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="123"/>
        <source>Stat Max Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="123"/>
        <source>Stat Min Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="124"/>
        <source>Stat Average Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="124"/>
        <source>Stat HGL Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="125"/>
        <source>Stat Pqrate Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="126"/>
        <source>Kwh Meter Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="126"/>
        <source>Kwh Sum By Interval</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="146"/>
        <source>P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="146"/>
        <source>Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="147"/>
        <source>Voltage</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="147"/>
        <source>Current</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="148"/>
        <source>Tap</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="148"/>
        <source>Temperature</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="149"/>
        <source>Cos</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="149"/>
        <source>Hz</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="150"/>
        <source>P Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="150"/>
        <source>Q Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="151"/>
        <source>Forward P Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="151"/>
        <source>Rev P Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="152"/>
        <source>Forward Q Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="152"/>
        <source>Rev Q Kwh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="153"/>
        <source>A P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="153"/>
        <source>B P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="154"/>
        <source>C P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="154"/>
        <source>0 P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="155"/>
        <source>A Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="155"/>
        <source>B Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="156"/>
        <source>C Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="156"/>
        <source>0 Q</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="157"/>
        <source>A Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="157"/>
        <source>B Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="158"/>
        <source>C Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="158"/>
        <source>0 Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="159"/>
        <source>AB Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="159"/>
        <source>BC Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="160"/>
        <source>CA Vol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="160"/>
        <source>A I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="161"/>
        <source>B I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="161"/>
        <source>C I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="162"/>
        <source>0 I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="163"/>
        <source>PLOSS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="164"/>
        <source>EFFIDX</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="165"/>
        <source>BEARLEVEL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="166"/>
        <source>LOADLEVEL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="167"/>
        <source>VOLUB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="168"/>
        <source>CRTUB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="169"/>
        <source>UNITCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="170"/>
        <source>MDQUOTA</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="171"/>
        <source>TOTCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="172"/>
        <source>HIGHCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="173"/>
        <source>MEDCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="174"/>
        <source>LOWCONSUMER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="175"/>
        <source>TOTCOST</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="176"/>
        <source>HIGHCOST</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="177"/>
        <source>MEDCOST</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="178"/>
        <source>LOWCOST</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="213"/>
        <source>Breaker State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="213"/>
        <source>Switch State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="214"/>
        <source>Protect Signal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="214"/>
        <source>SGZ Signal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="215"/>
        <source>Normal State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="215"/>
        <source>A Breaker State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="216"/>
        <source>B Breaker State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="216"/>
        <source>C Breaker State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="217"/>
        <source>State1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="217"/>
        <source>State32</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="238"/>
        <source>Normal change times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="239"/>
        <source>Fault change times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="240"/>
        <source>YK times(YT times)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="241"/>
        <source>Change to open times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="242"/>
        <source>YK refuse change times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="243"/>
        <source>change to close times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="244"/>
        <source>change to 00 times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="245"/>
        <source>change to 11 times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="246"/>
        <source>close time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="247"/>
        <source>00 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="248"/>
        <source>11 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="266"/>
        <source>Rtu run time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="267"/>
        <source>Rtu falut time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="268"/>
        <source>Rtu stop times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="278"/>
        <source>Channel run time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="279"/>
        <source>Channel stop times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="283"/>
        <source>Statistic Item</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="284"/>
        <source>Statistic Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="925"/>
        <source>TimeToTime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1288"/>
        <location filename="jkhistorydatadlg.cpp" line="1300"/>
        <source>Fault Time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1289"/>
        <location filename="jkhistorydatadlg.cpp" line="1301"/>
        <source>Fault Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1532"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1532"/>
        <source>StationDesc</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1533"/>
        <source>JXAllRunTimes(hour)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1533"/>
        <source>JXAllRunNums</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1533"/>
        <source>JXLastStatTime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1534"/>
        <source>JXRunTimes(hour)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1534"/>
        <source>JXRunNums</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1534"/>
        <source>UserName</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1669"/>
        <source>no station selected!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkhistorydatadlg.cpp" line="1687"/>
        <source>LastJXTime startTime should less than or equal endTime!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="37"/>
        <source>his data month average</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="38"/>
        <source>his data month max</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="39"/>
        <source>his data month min</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="40"/>
        <source>his data month max time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="41"/>
        <source>his data month min time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="42"/>
        <source>his data stat qualified rate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="43"/>
        <source>his data real qualified time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="44"/>
        <source>his data real hover time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="45"/>
        <source>his data real lover time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="46"/>
        <source>his data stat hover rate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="47"/>
        <source>his data stat lover rate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="48"/>
        <source>his data stat all time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="82"/>
        <source>all data type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="106"/>
        <source>check all</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="107"/>
        <source>point desc</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="108"/>
        <source>stat month time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="237"/>
        <source>Input Err</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="237"/>
        <source>input invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="317"/>
        <location filename="jkmanStatCallback.cpp" line="324"/>
        <location filename="jkmanStatCallback.cpp" line="832"/>
        <location filename="jkmanStatCallback.cpp" line="835"/>
        <location filename="jkmanStatCallback.cpp" line="1011"/>
        <location filename="jkmanStatCallback.cpp" line="1014"/>
        <source>info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="317"/>
        <source>Save successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="324"/>
        <source>None select or None to be changed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="423"/>
        <source>Cancel Err</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="423"/>
        <source>Cancel data change failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="453"/>
        <source>Please select time first</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="642"/>
        <source>Please select point first</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="647"/>
        <source>Please select stat time first</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="657"/>
        <source>point is not exist!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="711"/>
        <location filename="jkmanStatCallback.cpp" line="721"/>
        <source>current point stat time data exist!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="735"/>
        <source>add failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="832"/>
        <source>None selected need to delete !</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="835"/>
        <source>Do you want to delete current selected %1 Points</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="908"/>
        <source>all</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="935"/>
        <source>Open CsvFile</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="935"/>
        <source>Files (*.csv)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="947"/>
        <source>This operation will save the db directly.Sure to continue?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="1011"/>
        <source>import csvfile complete!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkmanStatCallback.cpp" line="1014"/>
        <source>import result: failed(%1),skip(%2)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkrecallCallback.cpp" line="206"/>
        <source>fetch Fault data failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkrecallCallback.cpp" line="222"/>
        <source>%1Year%2Mon%3Day%4Hour%5Min%6Sec%7Mse</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkrecallCallback.cpp" line="251"/>
        <location filename="jkrecallCallback.cpp" line="256"/>
        <source>SelectFault</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkrecallCallback.cpp" line="252"/>
        <source>Please select fault source!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkrecallCallback.cpp" line="257"/>
        <source>Are you sure to delete fault source?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkrecallCallback.cpp" line="314"/>
        <location filename="jkrecallCallback.cpp" line="420"/>
        <source>No Data.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkstatisCallback.cpp" line="78"/>
        <source>Statistic Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="jkstatisCallback.cpp" line="106"/>
        <source>Have no data from database!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="utcurvedatasetting.cpp" line="1117"/>
        <source>Time:%1:%2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="1121"/>
        <source>Time:%1Day</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="1124"/>
        <source>Time:%1Month</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="850"/>
        <location filename="utcurvedatasetting.cpp" line="964"/>
        <location filename="utcurvedatasetting.cpp" line="968"/>
        <location filename="utcurvedatasetting.cpp" line="975"/>
        <location filename="utcurvedatasetting.cpp" line="979"/>
        <location filename="utcurvedatasetting.cpp" line="1130"/>
        <location filename="utcurvedatasetting.cpp" line="1137"/>
        <source>Time:%1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="451"/>
        <location filename="utcurvedatasetting.cpp" line="605"/>
        <location filename="utcurvedatasetting.cpp" line="870"/>
        <location filename="utcurvedatasetting.cpp" line="1003"/>
        <location filename="utcurvedatasetting.cpp" line="1225"/>
        <location filename="utcurvedatasetting.cpp" line="1249"/>
        <source>%1 Big:%2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="455"/>
        <location filename="utcurvedatasetting.cpp" line="609"/>
        <location filename="utcurvedatasetting.cpp" line="874"/>
        <location filename="utcurvedatasetting.cpp" line="1007"/>
        <location filename="utcurvedatasetting.cpp" line="1229"/>
        <location filename="utcurvedatasetting.cpp" line="1253"/>
        <source>%1 Low:%2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="458"/>
        <location filename="utcurvedatasetting.cpp" line="612"/>
        <source>  Time: %1   Value: %2</source>
        <oldsource> Time:%1 Value:%2</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="878"/>
        <location filename="utcurvedatasetting.cpp" line="1011"/>
        <location filename="utcurvedatasetting.cpp" line="1232"/>
        <location filename="utcurvedatasetting.cpp" line="1256"/>
        <source>Value:	%1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="895"/>
        <location filename="utcurvedatasetting.cpp" line="1031"/>
        <location filename="utcurvedatasetting.cpp" line="1036"/>
        <location filename="utcurvedatasetting.cpp" line="1180"/>
        <location filename="utcurvedatasetting.cpp" line="1185"/>
        <location filename="utcurvedatasetting.cpp" line="1304"/>
        <location filename="utcurvedatasetting.cpp" line="1327"/>
        <source>MaxValue:%1 	 Time:%2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="905"/>
        <location filename="utcurvedatasetting.cpp" line="1048"/>
        <location filename="utcurvedatasetting.cpp" line="1053"/>
        <location filename="utcurvedatasetting.cpp" line="1197"/>
        <location filename="utcurvedatasetting.cpp" line="1202"/>
        <location filename="utcurvedatasetting.cpp" line="1314"/>
        <location filename="utcurvedatasetting.cpp" line="1337"/>
        <source>MinValue:%1 	 Time:%2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utcurvedatasetting.cpp" line="882"/>
        <location filename="utcurvedatasetting.cpp" line="1016"/>
        <location filename="utcurvedatasetting.cpp" line="1260"/>
        <source>(InValid)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TrendCurveDlg</name>
    <message>
        <location filename="trendcurvedlg.ui" line="14"/>
        <source>TrendCurveDlg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.ui" line="26"/>
        <source>1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.ui" line="39"/>
        <source>0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.ui" line="46"/>
        <source>Y Set</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.ui" line="52"/>
        <location filename="trendcurvedlg.ui" line="137"/>
        <source>MinValue:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.ui" line="75"/>
        <location filename="trendcurvedlg.ui" line="160"/>
        <source>MaxValue:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.ui" line="111"/>
        <location filename="trendcurvedlg.ui" line="196"/>
        <source>Set</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.ui" line="131"/>
        <source>Limits Set</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.ui" line="219"/>
        <source>Ok</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="trendcurvedlg.cpp" line="64"/>
        <source>Trend curve</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UtActSpeedDlg</name>
    <message>
        <location filename="utactspeeddlg.ui" line="26"/>
        <source>Dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="39"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="120"/>
        <location filename="utactspeeddlg.ui" line="140"/>
        <location filename="utactspeeddlg.ui" line="147"/>
        <location filename="utactspeeddlg.ui" line="154"/>
        <location filename="utactspeeddlg.ui" line="161"/>
        <source>TextLabel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="198"/>
        <source>Active Speed:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="218"/>
        <source>on</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="225"/>
        <source>off</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="232"/>
        <source>all</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="241"/>
        <source>config</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="50"/>
        <source>curvetab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="183"/>
        <source>tabletab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.ui" line="32"/>
        <source>addpoint</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.cpp" line="58"/>
        <location filename="utactspeeddlg.cpp" line="727"/>
        <location filename="utactspeeddlg.cpp" line="745"/>
        <location filename="utactspeeddlg.cpp" line="763"/>
        <source>%1 Active Speed curve(%2)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="utactspeeddlg.cpp" line="148"/>
        <source>This point saveinfo is not set ok,please check!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>YCStatisticInfoDlg</name>
    <message>
        <location filename="ycstatisticinfodlg.ui" line="14"/>
        <source>YCStatisticInfoDlg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.ui" line="49"/>
        <source>Ok</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="38"/>
        <location filename="ycstatisticinfodlg.cpp" line="75"/>
        <location filename="ycstatisticinfodlg.cpp" line="81"/>
        <location filename="ycstatisticinfodlg.cpp" line="100"/>
        <source>Max value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="39"/>
        <location filename="ycstatisticinfodlg.cpp" line="76"/>
        <location filename="ycstatisticinfodlg.cpp" line="82"/>
        <location filename="ycstatisticinfodlg.cpp" line="101"/>
        <source>Min value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="40"/>
        <location filename="ycstatisticinfodlg.cpp" line="102"/>
        <source>Average value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="41"/>
        <location filename="ycstatisticinfodlg.cpp" line="103"/>
        <source>HGL value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="42"/>
        <location filename="ycstatisticinfodlg.cpp" line="104"/>
        <source>Pqrate value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="43"/>
        <location filename="ycstatisticinfodlg.cpp" line="111"/>
        <source>HLDIS value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="44"/>
        <location filename="ycstatisticinfodlg.cpp" line="105"/>
        <source>HOVER1 count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="45"/>
        <source>HOVER2 count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="46"/>
        <source>HOVER3 count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="47"/>
        <location filename="ycstatisticinfodlg.cpp" line="106"/>
        <source>LOVER1 count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="48"/>
        <source>LOVER2 count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="49"/>
        <source>LOVER3 count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="50"/>
        <location filename="ycstatisticinfodlg.cpp" line="107"/>
        <source>HOVER1 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="51"/>
        <source>HOVER2 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="52"/>
        <source>HOVER3 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="53"/>
        <location filename="ycstatisticinfodlg.cpp" line="108"/>
        <source>LOVER1 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="54"/>
        <source>LOVER2 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="55"/>
        <source>LOVER3 time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="109"/>
        <source>HMAX value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="110"/>
        <source>LMIN value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="112"/>
        <source>HAVG value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="113"/>
        <source>TabNum value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="147"/>
        <source>KWHSUM value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="148"/>
        <source>PERIOD1 KWHSUM value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="149"/>
        <source>PERIOD2 KWHSUM value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="150"/>
        <source>PERIOD3 KWHSUM value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="151"/>
        <source>PERIOD4 KWHSUM value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="162"/>
        <source>Statistic Item</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="163"/>
        <source>Statistic Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="164"/>
        <source>Occur Date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="231"/>
        <source>Real</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="247"/>
        <location filename="ycstatisticinfodlg.cpp" line="251"/>
        <source>His</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="264"/>
        <source>Day</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="280"/>
        <source>Month</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="299"/>
        <source>Year</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="304"/>
        <source>Statistic Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="ycstatisticinfodlg.cpp" line="413"/>
        <source>yyyy-MM-dd hh:mm:ss</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>addspeedpoint</name>
    <message>
        <location filename="addspeedpoint.ui" line="14"/>
        <source>Dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.ui" line="39"/>
        <source>PointName:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.ui" line="49"/>
        <source>search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.ui" line="56"/>
        <source>Area:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.ui" line="73"/>
        <source>Station:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.ui" line="119"/>
        <source>PointList</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.ui" line="151"/>
        <source>&gt;&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.ui" line="158"/>
        <source>&lt;&lt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.ui" line="214"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.cpp" line="117"/>
        <location filename="addspeedpoint.cpp" line="132"/>
        <location filename="addspeedpoint.cpp" line="150"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.cpp" line="117"/>
        <source>Relation Device devid is 0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.cpp" line="132"/>
        <source>current point is too more, can not add.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="addspeedpoint.cpp" line="150"/>
        <source>current compare point can not delete.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>curecfg</name>
    <message>
        <location filename="curecfg.ui" line="14"/>
        <source>Dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="curecfg.ui" line="20"/>
        <source>normalvalue:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="curecfg.ui" line="33"/>
        <source>drivalue:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="curecfg.ui" line="43"/>
        <source>visiblenum:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
