#include "utcurvexygridtick.h"


UTCurveXYGridTick::UTCurveXYGridTick()
{
    m_major=4;
    m_minor=4;
}

UTCurveXYGridTick::UTCurveXYGridTick(unsigned int tick_major,unsigned int tick_minor)
{
    setTickMajor(tick_major);
    setTickMinor(tick_minor);
}


unsigned int UTCurveXYGridTick::tickMajor(void) const
{
    return(m_major);
}

void UTCurveXYGridTick::setTickMajor(unsigned int tick)
{
    if(tick<2) tick=2;
    m_major=tick;
}

unsigned int UTCurveXYGridTick::tickMinor(void) const
{
    return(m_minor);
}

void UTCurveXYGridTick::setTickMinor(unsigned int tick)
{
    if(tick<2) tick=2;
    m_minor=tick;
}
